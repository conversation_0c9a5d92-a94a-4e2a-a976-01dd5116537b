import CustomButton from '@/components/CustomButton';
import CustomModal from '@/components/CustomModal';
import CustomTable from '@/components/CustomTable';
import {
  deleteUsingDELETE7,
  pageUsingGET5,
  saveUsingPOST7,
  updateUsingPUT7,
  uploadUsingPOST7,
} from '@/services/homepage/shouyewailaidian';
import px from '@/utils/px';
import { CloudUploadOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Space,
  Upload,
} from 'antd';
import type { RcFile } from 'antd/es/upload';
import React, { useEffect, useState } from 'react';
import styles from './index.sass';

interface Props {
  name?: string;
  infoOpen: boolean;
  onCancelInfo: () => void;
}

interface TableRecord {
  time: number;
  externalElectricity: number;
  id: number;
}

const Index: React.FC<Props> = (props: Props) => {
  const { infoOpen, onCancelInfo } = props;
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<TableRecord | null>(null);
  const [tableData, setTableData] = useState<TableRecord[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();

  const fetchTableData = async (page: number = 1) => {
    try {
      const response = await pageUsingGET5({
        current: page,
        size: 6,
      });
      if (response.code === 1000) {
        setTableData(response.data.records);
        setTotalRecords(response.data.total);
      }
    } catch (error) {
      message.error('获取数据失败');
    }
  };

  useEffect(() => {
    if (infoOpen) {
      fetchTableData();
    }
  }, [infoOpen]);

  const handleEdit = (record: TableRecord) => {
    setCurrentRecord(record);
    form.setFieldsValue({
      time: record.time,
      power: record.externalElectricity,
    });
    setEditModalVisible(true);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const apiValues = {
        id: currentRecord?.id,
        time: values.time,
        externalElectricity: values.power,
      };

      const response = await updateUsingPUT7(
        { id: currentRecord?.id },
        apiValues,
      );

      if (response.code === 1000) {
        message.success('保存成功');
        setEditModalVisible(false);
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('Save failed:', error);
      message.error('保存失败');
    }
  };

  const handleDelete = async (record: TableRecord) => {
    try {
      const response = await deleteUsingDELETE7({
        id: record.id,
      });

      if (response.code === 1000) {
        message.success('删除成功');
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('Delete failed:', error);
      message.error('删除失败');
    }
  };

  const handleAdd = () => {
    addForm.resetFields();
    setAddModalVisible(true);
  };

  const handleAddSave = async () => {
    try {
      const values = await addForm.validateFields();
      const apiValues = {
        time: Number(values.time),
        externalElectricity: Number(values.power),
      };

      const response = await saveUsingPOST7(apiValues);

      if (response.code === 1000) {
        message.success('添加成功');
        setAddModalVisible(false);
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '添加失败');
      }
    } catch (error) {
      console.error('Add failed:', error);
      message.error('添加失败');
    }
  };

  const handleUpload = async (file: RcFile) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await uploadUsingPOST7(formData);

      if (response.code === 1000) {
        message.success('上传成功');
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '上传失败');
      }
      return false;
    } catch (error) {
      console.error('Upload failed:', error);
      message.error('上传失败');
      return false;
    }
  };

  const columns = [
    { title: '时间', dataIndex: 'time', key: 'time' },
    {
      title: '外来电（万千瓦）',
      dataIndex: 'externalElectricity',
      key: 'externalElectricity',
    },
    {
      title: '操作',
      key: 'operation',
      render: (_: any, record: TableRecord) => (
        <Space>
          <Button
            style={{ fontSize: px(14) }}
            type="link"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger style={{ fontSize: px(14) }}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const formItems = (
    <Row gutter={8}>
      <Col span={12}>
        <Form.Item
          label="时间"
          name="time"
          rules={[{ required: true, message: '请输入时间' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          label="外来电（万千瓦）"
          name="power"
          rules={[{ required: true, message: '请输入外来电功率' }]}
        >
          <Input />
        </Form.Item>
      </Col>
    </Row>
  );

  return (
    <div className={styles.container}>
      <CustomModal
        open={infoOpen}
        title="外来电"
        onCancel={onCancelInfo}
        footer={null}
        width={'fit-content'}
        className={styles.customModal}
        centered={true}
      >
        <div className={styles.box}>
          <div style={{ display: 'flex' }}>
            <CustomButton icon={<PlusOutlined />} onClick={handleAdd}>
              添加
            </CustomButton>
            <Upload beforeUpload={handleUpload} showUploadList={false}>
              <CustomButton icon={<CloudUploadOutlined />}>导入</CustomButton>
            </Upload>
          </div>
          <CustomTable
            className={styles.customTable}
            columns={columns}
            dataSource={tableData}
            pagination={{
              current: currentPage,
              pageSize: 6,
              total: totalRecords,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              onChange: (page) => {
                setCurrentPage(page);
                fetchTableData(page);
              },
            }}
          />
        </div>
      </CustomModal>

      {/* 编辑弹窗 */}
      <Modal
        title="编辑节点"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={handleSave}
        width={px(500)}
      >
        <Form form={form} layout="vertical" initialValues={currentRecord}>
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>

      {/* 添加弹窗 */}
      <Modal
        title="添加节点"
        open={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onOk={handleAddSave}
        width={px(500)}
      >
        <Form form={addForm} layout="vertical">
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>
    </div>
  );
};

export default Index;
