// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 导出数据 GET /masc/PostDisasterStorageCurve/download */
export async function downloadUsingGET4(options?: { [key: string]: any }) {
  return request<any>('/masc/PostDisasterStorageCurve/download', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询全部数据 GET /masc/PostDisasterStorageCurve/query */
export async function queryUsingGET4(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/masc/PostDisasterStorageCurve/query', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/PostDisasterStorageCurve/upload */
export async function uploadUsingPOST20(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/PostDisasterStorageCurve/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
