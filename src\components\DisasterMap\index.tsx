import px from '@/utils/px';
import AMapLoader from '@amap/amap-jsapi-loader';
import { useEffect, useRef } from 'react';
import styles from './index.sass';

// 声明全局AMap对象
declare global {
  interface Window {
    AMap: any;
    _AMapSecurityConfig: {
      securityJsCode: string;
    };
  }
}

// 定义不同灾害类型的受影响区域
const disasterAffectedAreas: Record<string, string[]> = {
  typhoon: ['宁波市', '台州市', '温州市'], // 台风影响宁波、台州、温州
  tsunami: ['宁波市', '台州市', '温州市'], // 海啸影响宁波、台州、温州
  ice_snow: [], // 冰雪影响全境，不需要特别指定
  thunderstorm: ['杭州市', '湖州市', '嘉兴市', '绍兴市'], // 雷暴影响浙北地区
  flood: ['杭州市', '绍兴市', '宁波市'], // 洪涝影响杭州、绍兴、宁波
};

interface DisasterMapProps {
  disasterType: string;
}

const DisasterMap: React.FC<DisasterMapProps> = ({ disasterType }) => {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const polygonsRef = useRef<any[]>([]);

  // 清除灾害区域多边形，但保留热区
  const clearPolygons = () => {
    if (mapInstanceRef.current && polygonsRef.current.length > 0) {
      polygonsRef.current.forEach(polygon => {
        // 只移除灾害区域多边形，不移除热区
        if (polygon.getExtData() && polygon.getExtData().type === 'disasterArea') {
          mapInstanceRef.current.remove(polygon);
        }
      });
      // 过滤掉已移除的多边形
      polygonsRef.current = polygonsRef.current.filter(
        polygon => !(polygon.getExtData() && polygon.getExtData().type === 'disasterArea')
      );
    }
  };

  // 高亮指定城市
  const highlightCities = (map: any, cities: string[]) => {
    clearPolygons();

    // 创建行政区查询实例
    const districtSearch = new window.AMap.DistrictSearch({
      subdistrict: 0,
      extensions: 'all',
      level: 'city'
    });

    // 如果是冰雪灾害，高亮整个浙江省
    if (disasterType === 'ice_snow') {
      districtSearch.search('浙江省', (status: string, result: any) => {
        if (status === 'complete' && result.districtList.length > 0) {
          const boundaries = result.districtList[0].boundaries;

          // 创建多边形覆盖物
          boundaries.forEach((boundary: any) => {
            const polygon = new window.AMap.Polygon({
              path: boundary,
              strokeColor: '#3366FF',
              strokeWeight: 2,
              strokeOpacity: 0.8,
              fillColor: '#3366FF',
              fillOpacity: 0.3,
              zIndex: 40, // 低于热区的zIndex
              extData: { type: 'disasterArea', disasterType: 'ice_snow' }
            });

            // 将多边形添加到地图
            map.add(polygon);
            polygonsRef.current.push(polygon);
          });
        }
      });
      return;
    }

    // 对每个城市进行高亮
    cities.forEach(city => {
      districtSearch.search(`浙江省 ${city}`, (status: string, result: any) => {
        if (status === 'complete' && result.districtList.length > 0) {
          const boundaries = result.districtList[0].boundaries;

          // 创建多边形覆盖物
          boundaries.forEach((boundary: any) => {
            const polygon = new window.AMap.Polygon({
              path: boundary,
              strokeColor: '#FF0000',
              strokeWeight: 2,
              strokeOpacity: 0.8,
              fillColor: '#FF0000',
              fillOpacity: 0.3,
              zIndex: 40, // 低于热区的zIndex
              extData: { type: 'disasterArea', city: city, disasterType: disasterType }
            });

            // 将多边形添加到地图
            map.add(polygon);
            polygonsRef.current.push(polygon);
          });
        }
      });
    });
  };


    // 添加热区到地图
  const addHotAreas = (map: any, disasterType: string) => {
    // 清除现有热区
    clearHotAreas();

    console.log('添加新的热区...');

    // 为不同灾害类型定义不同的热区
    // 定义灾害类型对应的热区
    const disasterHotAreas: Record<string, any[]> = {
      // 台风热区 - 主要影响沿海地区
      typhoon: [
        {
          name: '宁波沿海区域',
          path: [
            [121.3, 30.3], // 宁波沿海区域 - 不规则多边形
            [121.8, 30.4],
            [122.1, 30.2],
            [122.0, 29.9],
            [121.7, 29.7],
            [121.4, 29.8],
            [121.2, 30.0],
            [121.3, 30.3]
          ],
          color: 'rgba(255, 0, 0, 0.6)', // 半透明红，增加透明度
          strokeColor: '#ff0000',
          strokeWeight: 4,
          riskLevel: '高'
        },
        {
          name: '台州沿海区域',
          path: [
            [121.2, 29.1], // 台州沿海区域 - 不规则多边形
            [121.6, 29.2],
            [121.8, 29.0],
            [121.7, 28.7],
            [121.4, 28.5],
            [121.1, 28.6],
            [121.0, 28.8],
            [121.2, 29.1]
          ],
          color: 'rgba(255, 100, 0, 0.6)', // 半透明橙红，增加透明度
          strokeColor: '#ff6400',
          strokeWeight: 4,
          riskLevel: '高'
        },
        {
          name: '温州沿海区域',
          path: [
            [120.5, 28.2], // 温州沿海区域 - 不规则多边形
            [121.0, 28.3],
            [121.3, 28.1],
            [121.2, 27.8],
            [120.9, 27.5],
            [120.5, 27.6],
            [120.3, 27.9],
            [120.5, 28.2]
          ],
          color: 'rgba(255, 165, 0, 0.6)', // 半透明橙，增加透明度
          strokeColor: '#ff9900',
          strokeWeight: 4,
          riskLevel: '高'
        }
      ],

      // 海啸热区 - 与台风类似，但范围更广
      tsunami: [
        {
          name: '宁波沿海区域',
          path: [
            [121.2, 30.4], // 宁波沿海区域 - 不规则多边形，比台风范围更大
            [121.9, 30.5],
            [122.3, 30.2],
            [122.1, 29.8],
            [121.6, 29.6],
            [121.2, 29.7],
            [121.0, 30.0],
            [121.2, 30.4]
          ],
          color: 'rgba(0, 0, 255, 0.6)', // 半透明蓝，增加透明度
          strokeColor: '#0000ff',
          strokeWeight: 4,
          riskLevel: '极高'
        },
        {
          name: '台州沿海区域',
          path: [
            [121.1, 29.2], // 台州沿海区域 - 不规则多边形，比台风范围更大
            [121.7, 29.3],
            [122.0, 29.0],
            [121.8, 28.6],
            [121.3, 28.4],
            [121.0, 28.5],
            [120.8, 28.8],
            [121.1, 29.2]
          ],
          color: 'rgba(65, 105, 225, 0.6)', // 半透明皇家蓝，增加透明度
          strokeColor: '#4169e1',
          strokeWeight: 4,
          riskLevel: '极高'
        },
        {
          name: '温州沿海区域',
          path: [
            [120.4, 28.3], // 温州沿海区域 - 不规则多边形，比台风范围更大
            [121.1, 28.4],
            [121.5, 28.1],
            [121.3, 27.7],
            [120.8, 27.4],
            [120.4, 27.5],
            [120.2, 27.9],
            [120.4, 28.3]
          ],
          color: 'rgba(30, 144, 255, 0.6)', // 半透明道奇蓝，增加透明度
          strokeColor: '#1e90ff',
          strokeWeight: 4,
          riskLevel: '极高'
        }
      ],

      // 冰雪热区 - 覆盖全省，但北部更严重
      ice_snow: [
        {
          name: '杭州湾区域',
          path: [
            [119.8, 30.5], // 杭州湾区域 - 不规则多边形
            [120.5, 30.6],
            [121.0, 30.5],
            [121.0, 30.0],
            [120.5, 29.8],
            [119.8, 29.9],
            [119.6, 30.2],
            [119.8, 30.5]
          ],
          color: 'rgba(0, 191, 255, 0.6)', // 半透明深天蓝，增加透明度
          strokeColor: '#00bfff',
          strokeWeight: 4,
          riskLevel: '中'
        },
        {
          name: '湖州嘉兴区域',
          path: [
            [119.5, 31.0], // 湖州嘉兴区域 - 不规则多边形
            [120.2, 31.1],
            [120.5, 30.9],
            [120.4, 30.6],
            [119.8, 30.5],
            [119.4, 30.7],
            [119.5, 31.0]
          ],
          color: 'rgba(135, 206, 250, 0.6)', // 半透明浅天蓝，增加透明度
          strokeColor: '#87cefa',
          strokeWeight: 4,
          riskLevel: '高'
        },
        {
          name: '金华衢州区域',
          path: [
            [119.2, 29.5], // 金华衢州区域 - 不规则多边形
            [119.8, 29.6],
            [120.1, 29.4],
            [120.0, 29.0],
            [119.5, 28.8],
            [119.0, 29.0],
            [119.2, 29.5]
          ],
          color: 'rgba(176, 224, 230, 0.6)', // 半透明粉蓝色，增加透明度
          strokeColor: '#b0e0e6',
          strokeWeight: 4,
          riskLevel: '中'
        }
      ],

      // 雷暴热区 - 主要影响北部地区
      thunderstorm: [
        {
          name: '杭州区域',
          path: [
            [119.8, 30.4], // 杭州区域 - 不规则多边形
            [120.3, 30.5],
            [120.5, 30.3],
            [120.4, 30.0],
            [120.0, 29.9],
            [119.7, 30.1],
            [119.8, 30.4]
          ],
          color: 'rgba(148, 0, 211, 0.6)', // 半透明紫罗兰，增加透明度
          strokeColor: '#9400d3',
          strokeWeight: 4,
          riskLevel: '高'
        },
        {
          name: '湖州区域',
          path: [
            [119.5, 30.9], // 湖州区域 - 不规则多边形
            [120.0, 31.0],
            [120.2, 30.8],
            [120.1, 30.5],
            [119.7, 30.4],
            [119.4, 30.6],
            [119.5, 30.9]
          ],
          color: 'rgba(138, 43, 226, 0.6)', // 半透明蓝紫色，增加透明度
          strokeColor: '#8a2be2',
          strokeWeight: 4,
          riskLevel: '高'
        },
        {
          name: '嘉兴区域',
          path: [
            [120.5, 30.9], // 嘉兴区域 - 不规则多边形
            [121.0, 31.0],
            [121.2, 30.8],
            [121.1, 30.5],
            [120.7, 30.4],
            [120.4, 30.6],
            [120.5, 30.9]
          ],
          color: 'rgba(186, 85, 211, 0.6)', // 半透明中兰花紫，增加透明度
          strokeColor: '#ba55d3',
          strokeWeight: 4,
          riskLevel: '高'
        },
        {
          name: '绍兴区域',
          path: [
            [120.3, 30.2], // 绍兴区域 - 不规则多边形
            [120.8, 30.3],
            [121.0, 30.1],
            [120.9, 29.8],
            [120.5, 29.7],
            [120.2, 29.9],
            [120.3, 30.2]
          ],
          color: 'rgba(221, 160, 221, 0.6)', // 半透明梅红色，增加透明度
          strokeColor: '#dda0dd',
          strokeWeight: 4,
          riskLevel: '中'
        }
      ],

      // 洪涝热区 - 主要影响杭州、绍兴、宁波
      flood: [
        {
          name: '杭州区域',
          path: [
            [119.7, 30.4], // 杭州区域 - 不规则多边形
            [120.3, 30.5],
            [120.6, 30.3],
            [120.5, 29.9],
            [120.0, 29.8],
            [119.6, 30.0],
            [119.7, 30.4]
          ],
          color: 'rgba(0, 128, 0, 0.6)', // 半透明绿色，增加透明度
          strokeColor: '#008000',
          strokeWeight: 4,
          riskLevel: '高'
        },
        {
          name: '绍兴区域',
          path: [
            [120.4, 30.2], // 绍兴区域 - 不规则多边形
            [120.9, 30.3],
            [121.1, 30.1],
            [121.0, 29.7],
            [120.6, 29.6],
            [120.3, 29.8],
            [120.4, 30.2]
          ],
          color: 'rgba(34, 139, 34, 0.6)', // 半透明森林绿，增加透明度
          strokeColor: '#228b22',
          strokeWeight: 4,
          riskLevel: '高'
        },
        {
          name: '宁波区域',
          path: [
            [121.2, 30.0], // 宁波区域 - 不规则多边形
            [121.7, 30.1],
            [121.9, 29.9],
            [121.8, 29.6],
            [121.4, 29.5],
            [121.1, 29.7],
            [121.2, 30.0]
          ],
          color: 'rgba(50, 205, 50, 0.6)', // 半透明酸橙绿，增加透明度
          strokeColor: '#32cd32',
          strokeWeight: 4,
          riskLevel: '高'
        }
      ]
    };

    // 根据当前灾害类型选择对应的热区
    const hotAreas = disasterHotAreas[disasterType] || disasterHotAreas.typhoon;

    console.log(`当前灾害类型: ${disasterType}, 显示对应热区:`, hotAreas);

    // 创建并添加所有热区
    hotAreas.forEach((area, index) => {
      console.log(`开始创建热区 ${index + 1}: ${area.name}`);

      try {
        // 创建热区多边形，使用更明显的样式
        const polygon = new window.AMap.Polygon({
          path: area.path,
          strokeColor: area.strokeColor,
          strokeOpacity: 1,
          strokeWeight: area.strokeWeight || 4,
          fillColor: area.color,
          fillOpacity: 0.7, // 增加填充透明度
          zIndex: 150, // 确保热区在最上层
          extData: { type: 'hotArea', name: area.name, disasterType: disasterType },
          bubble: true // 允许事件冒泡
        });

        console.log(`热区 ${area.name} 创建成功:`, polygon);

        // 保存原始样式
        const originalStyle = {
          strokeColor: area.strokeColor,
          strokeOpacity: 1,
          strokeWeight: area.strokeWeight || 4,
          fillColor: area.color,
          fillOpacity: 0.7
        };

        // 鼠标悬停样式
        const hoverStyle = {
          strokeColor: '#ffffff',
          strokeOpacity: 1,
          strokeWeight: 5,
          fillColor: area.color,
          fillOpacity: 0.9
        };

        // 添加鼠标悬停事件
        polygon.on('mouseover', () => {
          console.log(`鼠标悬停在热区 ${area.name} 上`);
          polygon.setOptions(hoverStyle);
          // 显示简单提示
          map.setDefaultCursor('pointer');
        });

        // 添加鼠标离开事件
        polygon.on('mouseout', () => {
          polygon.setOptions(originalStyle);
          map.setDefaultCursor('default');
        });

        // 添加点击事件
        polygon.on('click', () => {
          console.log(`点击了热区 ${area.name}`);
          // 创建信息窗体
          // 根据风险等级设置颜色
          let riskColor = '#ff9900'; // 默认橙色
          if (area.riskLevel === '极高') {
            riskColor = '#ff0000'; // 红色
          } else if (area.riskLevel === '高') {
            riskColor = '#ff9900'; // 橙色
          } else if (area.riskLevel === '中') {
            riskColor = '#ffff00'; // 黄色
          } else if (area.riskLevel === '低') {
            riskColor = '#00ff00'; // 绿色
          }

          const infoWindow = new window.AMap.InfoWindow({
            content: `<div style="padding: 15px; background-color: rgba(0,0,0,0.8); color: white; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.5); font-size: 14px; min-width: 200px;">
                        <h3 style="margin: 0 0 10px 0; color: #fff; font-size: 16px; border-bottom: 1px solid rgba(255,255,255,0.3); padding-bottom: 5px;">${area.name}</h3>
                        <p style="margin: 0; line-height: 1.5;">灾害类型: <span style="color: #3399ff; font-weight: bold;">${
                          disasterType === 'typhoon' ? '台风' :
                          disasterType === 'tsunami' ? '海啸' :
                          disasterType === 'ice_snow' ? '冰雪' :
                          disasterType === 'thunderstorm' ? '雷暴' :
                          disasterType === 'flood' ? '洪涝' : '未知'
                        }</span></p>
                        <p style="margin: 5px 0 0; line-height: 1.5;">风险等级: <span style="color: ${riskColor}; font-weight: bold;">${area.riskLevel || '未知'}</span></p>
                        <p style="margin: 5px 0 0; line-height: 1.5;">影响范围: <span style="color: #ffffff;">${area.name}及周边</span></p>
                      </div>`,
            offset: new window.AMap.Pixel(0, -30),
            autoMove: true,
            closeWhenClickMap: true
          });

          // 打开信息窗体
          infoWindow.open(map, polygon.getBounds().getCenter());
        });

        // 将热区添加到地图
        console.log(`正在将热区 ${area.name} 添加到地图...`);
        map.add(polygon);
        console.log(`热区 ${area.name} 已添加到地图`);

        // 添加热区中心的标记，便于调试
        const center = polygon.getBounds().getCenter();
        const marker = new window.AMap.Marker({
          position: center,
          title: area.name,
          label: {
            content: area.name,
            direction: 'top'
          }
        });
        map.add(marker);
        console.log(`已在热区 ${area.name} 中心添加标记:`, center);

        // 保存热区多边形和标记的引用
        hotAreaPolygonsRef.current.push({
          polygon: polygon,
          marker: marker,
          area: area
        });

      } catch (error) {
        console.error(`创建热区 ${area.name} 失败:`, error);
      }
    });
  };



  useEffect(() => {
    // 设置密钥
    window._AMapSecurityConfig = {
      securityJsCode: '52b4cf4f44340f10fedbc716b1c5e5f1',
    };

    // 加载高德地图
    AMapLoader.load({
      key: '2ce45da06e505bdc29e14b5f0d54b153',
      version: '2.0',
      plugins: [
        'AMap.ToolBar',
        'AMap.ControlBar',
        'AMap.DistrictSearch',
        'AMap.Polygon',
        'AMap.InfoWindow',
      ],
    })
      .then((AMap) => {
        // 创建地图实例
        const map = new AMap.Map(mapContainerRef.current, {
          zoom: 8.2, // 增加缩放级别，使热区更加明显
          expandZoomRange: true,
          center: [120.5, 29.5], // 浙江省中心位置
          viewMode: '2D',
          mapStyle: 'amap://styles/dark', // 使用深色地图，使热区更加明显
          features: ['bg', 'point', 'road'],
          logoPosition: 'bottom',
          logoOffset: new AMap.Pixel(75, 20),
        });

        // 添加地图控件
        map.addControl(new AMap.ToolBar({
          position: 'RB'
        }));

        // 保存地图实例
        mapInstanceRef.current = map;

        // 获取当前灾害类型的受影响区域
        const affectedAreas = disasterAffectedAreas[disasterType] || [];

        // 高亮受影响区域
        highlightCities(map, affectedAreas);

        // 添加调试信息
        console.log('地图实例创建成功:', map);

        // 添加一个简单的标记，确认地图加载正常
        const marker = new window.AMap.Marker({
          position: [120.35, 29.52], // 浙江省中心位置
          title: '浙江省中心'
        });
        map.add(marker);
        console.log('添加了标记:', marker);

        // 添加热区
        addHotAreas(map, disasterType);



        // 添加省界
        new window.AMap.DistrictSearch({
          subdistrict: 0,
          extensions: 'all',
          level: 'province',
        }).search('浙江省', function (status: string, result: any) {
          if (status === 'complete' && result.districtList.length > 0) {
            const boundaries = result.districtList[0].boundaries;

            // 创建省界多边形
            const provinceBoundary = new window.AMap.Polygon({
              path: boundaries,
              strokeColor: '#FFFFFF',
              strokeWeight: 2,
              strokeOpacity: 1,
              fillColor: 'transparent',
              fillOpacity: 0,
              zIndex: 10,
            });

            // 将省界添加到地图
            map.add(provinceBoundary);
          }
        });
      })
      .catch(e => {
        console.error('地图加载失败', e);
      });

    // 组件卸载时清理
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.destroy();
        mapInstanceRef.current = null;
      }
    };
  }, [disasterType]); // 在组件挂载和灾害类型变化时执行

  // 存储热区多边形的引用
  const hotAreaPolygonsRef = useRef<any[]>([]);

  // 清除热区多边形
  const clearHotAreas = () => {
    if (mapInstanceRef.current && hotAreaPolygonsRef.current.length > 0) {
      console.log('清除热区多边形...');
      hotAreaPolygonsRef.current.forEach(item => {
        if (item.polygon) {
          mapInstanceRef.current.remove(item.polygon);
        }
        if (item.marker) {
          mapInstanceRef.current.remove(item.marker);
        }
      });
      hotAreaPolygonsRef.current = [];
    }
  };



  return (
    <div
      ref={mapContainerRef}
      className={styles.mapContainer}
      style={{ width: '100%', height: '100%', borderRadius: px(20) }}
    ></div>
  );
};

export default DisasterMap;
