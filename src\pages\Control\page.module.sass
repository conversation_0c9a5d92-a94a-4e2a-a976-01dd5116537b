@import "~@/assets/css/helper.sass"

.container 
  display: flex
  height: px(900)
  // :global(.ant-table-tbody)
  //   > tr
  //     &:nth-child(odd)
  //       > td
  //         background-color: black !important
  //         color: white // 确保文字在黑色背景上可见

  //     &:nth-child(even)
  //         background-image: url('@/assets/images/tbbg2.png'), url('@/assets/images/texture.svg')  !important
  //         background-size: 100% 100%, 10%, 100%
  //         background-repeat: no-repeat, repeat-x
  //         background-position: center,top left

  //     &:hover
  //       > td
  //         background-color: rgba(0, 0, 0, 0.1) !important
  //         background-image: none !important



.systemData
  width: px(1600)
  height: 100%
  display: flex
  justify-content: space-around
  align-items: center

.runData
  width: px(1600)
  height: px(900)
  display: flex
  flex-direction: column
  .table
    height: px(400)
    .content
      display: flex
      justify-content: space-around
      width: 100%
      height: px(330)
      .chart
        width: 40%
        position: relative

.charts
    display: flex
    width: 100%
    justify-content: space-around
    margin-top: px(72)
    .chart
        width: px(718)
        margin-right: px(29)
        .chartTitle
            height: px(52)
            line-height: px(52)
        .chartContent
            margin-top: px(39)
            width: px(718)
            height: px(420)
            // background-image: url('/chartBg.png')       
.sceneContainer
  display: flex
  flex-direction: column
  margin-left: px(50)
  overflow: hidden
  .risk
    margin-top: px(20)
    margin-bottom: px(20)
    display: flex
    flex-direction: row
    color: #fff
    align-items: center
    gap: px(30)
  .scenecharts
 
    display: flex
    flex-direction: column
    width: px(1400)
    .row
      display: flex 
      flex-direction: row
      justify-content: space-between 
      .chart
        .lineChart
          width: px(600)
          height: px(400)
.result
  display: flex
  flex-direction: row
  color: white
  margin-left: px(50)
  margin-top: px(30)
  height: 100%
  gap: px(30)
  
  .leftSection
    width: px(400)
    height: 100%
    
    .scenarioList
      width: 100%
      height: 100%
      display: flex
      flex-direction: column
      
      .tableTitle
        margin-bottom: px(15)
      
      :global(.ant-table-wrapper)
        flex: 1
        display: flex
        flex-direction: column
        
        .ant-spin-nested-loading
          flex: 1
          
          .ant-spin-container
            height: 100%
            display: flex
            flex-direction: column
            
            .ant-table
              flex: 1
              
              .ant-table-container
                height: 100%
                
                .ant-table-body
                  max-height: calc(100vh - #{px(200)})
                  overflow-y: auto
            
            .ant-pagination
              margin: px(16) 0
              flex-shrink: 0
              
              .ant-pagination-item,
              .ant-pagination-prev,
              .ant-pagination-next,
              .ant-pagination-jump-prev,
              .ant-pagination-jump-next
                background: transparent
                border-color: rgba(255, 255, 255, 0.2)
                a
                  color: rgba(255, 255, 255, 0.8)
                
                &:hover
                  border-color: #00aeff
                  a
                    color: #00aeff
                    
              .ant-pagination-item-active
                background: rgba(0, 174, 255, 0.2)
                border-color: #00aeff
                border: 1px solid #00aeff
                a
                  color: #00aeff
                  
              .ant-select-selector
                background: transparent
                border-color: rgba(255, 255, 255, 0.2)
                color: rgba(255, 255, 255, 0.8)
                
              .ant-pagination-options-quick-jumper
                color: rgba(255, 255, 255, 0.8)
                input
                  background: transparent
                  border-color: rgba(255, 255, 255, 0.2)
                  color: rgba(255, 255, 255, 0.8)
                  &:hover, &:focus
                    border-color: #00aeff

  .rightSection
    flex: 1
    display: flex
    flex-direction: column
    gap: px(20)
    width: px(1000)

    .parameterSection
      .indexDisplay
        display: grid
        grid-template-columns: repeat(3, 1fr)
        gap: px(20)
        margin-top: px(15)
        .indexItem
          font-size: px(16)
          display: flex
          flex-direction: row
          gap: px(20)
          .number
            color: #00aeff

    .chartsSection
      flex: 1
      display: flex
      flex-direction: column
      margin-top: px(20)
      .chart
          flex: 1
          height: px(350) 
 