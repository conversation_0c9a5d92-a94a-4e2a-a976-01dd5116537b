
import React, { useState } from 'react';
import styles from './index.module.sass';
import CustomPane from '@/components/CustomPane';
import ReactECharts from 'echarts-for-react';
import px from '@/utils/px';
interface FaultSettingsProps {
  setActiveId?: (id: string) => void;
}

const FaultSettings: React.FC<FaultSettingsProps> = ({ setActiveId }) => {
  const [isCalculating, setIsCalculating] = useState(false);
  const [buttonText, setButtonText] = useState('导入数据');
  const [isPulse, setIsPulse] = useState(false);

  // 空调物理参数
  const [acPhysicalParams, setAcPhysicalParams] = useState({
    ratedPower: 3.5, // 额定功率 kW
    energyRatio: 3.2  // 能效比
  });

  // 环境物理参数
  const [environmentParams, setEnvironmentParams] = useState({
    outdoorTemp: 35, // 等效热源 ℃
    indoorTemp: 26   // 等效热导 ℃
  });

  // 用户舒适度设定
  const [comfortSettings, setComfortSettings] = useState({
    userSetTemp: 24,      // 用户设定温度 ℃
    maxTempVariation: 2   // 最大可接受温差 ℃
  });

  // 评估结果
  const [evaluationResults, setEvaluationResults] = useState({
    capacity: 85,        // 备用容量 %
    responseTime: 5,     // 响应时间 min
    maintainTime: 45,    // 持续时间 min
    crawlRate: 92        // 爬坡率 %
  });

  // 功率变化曲线配置
  const powerChangeOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00aeff',
      borderWidth: 1,
      textStyle: { color: '#fff' },
      formatter: function(params: any) {
        let result = params[0].name + '<br/>';
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value} kW<br/>`;
        });
        return result;
      }
    },
    legend: {
      data: ['空调不参与需求侧响应', '接受并执行需求侧响应'],
      textStyle: { color: '#6affeb' },
      top: '5%'
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0:00', '2:00', '4:00', '6:00', '8:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00'],
      axisLine: { lineStyle: { color: '#00aeff' } },
      axisLabel: { color: '#6affeb' },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      name: '功率 (kW)',
      nameTextStyle: { color: '#6affeb' },
      axisLine: { lineStyle: { color: '#00aeff' } },
      axisLabel: { color: '#6affeb' },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 174, 255, 0.2)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '空调不参与需求侧响应',
        type: 'line',
        data: [2.5, 2.5, 2.8, 3.2, 3.5, 3.5, 3.5, 3.8, 3.5, 3.2, 2.8, 2.5],
        smooth: true,
        lineStyle: { width: 3, color: '#00aeff' },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: { color: '#00aeff' }
      },
      {
        name: '接受并执行需求侧响应',
        type: 'line',
        data: [2.5, 2.5, 2.0, 1.5, 2.8, 3.2, 3.0, 2.5, 2.0, 2.8, 3.2, 2.5],
        smooth: true,
        lineStyle: { width: 3, color: '#6affeb' },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: { color: '#6affeb' }
      }
    ]
  };

  // 室温变化曲线配置
  const temperatureChangeOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00f7ff',
      borderWidth: 1,
      textStyle: { color: '#fff' }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0:00', '2:00', '4:00', '6:00', '8:00', '10:00', '12:00'],
      axisLine: { lineStyle: { color: '#00f7ff' } },
      axisLabel: { color: '#6affeb' },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      name: '温度 (℃)',
      nameTextStyle: { color: '#6affeb' },
      axisLine: { lineStyle: { color: '#00f7ff' } },
      axisLabel: { color: '#6affeb' },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 247, 255, 0.2)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '室内温度',
        type: 'line',
        data: [24, 24, 25, 26, 25.5, 24.5, 24],
        smooth: true,
        lineStyle: { width: 3, color: '#00f7ff' },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: { color: '#00f7ff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 247, 255, 0.4)' },
              { offset: 1, color: 'rgba(0, 247, 255, 0.05)' }
            ]
          }
        }
      }
    ]
  };

  const handleAcParamChange = (field: keyof typeof acPhysicalParams, value: number) => {
    setAcPhysicalParams(prev => ({ ...prev, [field]: value }));
  };

  const handleEnvironmentParamChange = (field: keyof typeof environmentParams, value: number) => {
    setEnvironmentParams(prev => ({ ...prev, [field]: value }));
  };

  const handleComfortSettingChange = (field: keyof typeof comfortSettings, value: number) => {
    setComfortSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleImportData = () => {
    setIsCalculating(true);
    setButtonText('导入中...');

    setTimeout(() => {
      setButtonText('导入完成');
      setIsPulse(true);

      // 模拟计算结果更新
      setEvaluationResults({
        capacity: 85 + Math.random() * 10,
        responseTime: 5 + Math.random() * 3,
        maintainTime: 45 + Math.random() * 15,
        crawlRate: 92 + Math.random() * 5
      });

      setTimeout(() => {
        setIsPulse(false);
        setButtonText('重新导入');
        setIsCalculating(false);
      }, 2000);
    }, 1500);
  };

  return (
    <div className={styles.airConditioningCalculator}>
      <div className={styles.gridOverlay}></div>
      <div className={`${styles.glow} ${styles.glow1}`}></div>
      <div className={`${styles.glow} ${styles.glow2}`}></div>

      <div className={styles.container}>
        {/* 左侧输入面板 */}
        <div className={styles.inputPanel}>
          {/* 空调物理参数 */}
          <CustomPane title="空调物理参数" button={
            <button
              className={`${styles.importButton} ${isPulse ? styles.pulse : ''}`}
              onClick={handleImportData}
              disabled={isCalculating}
            >
              {buttonText}
            </button>
          }>
            <div className={styles.inputSection}>
              <div className={styles.inputGrid}>
                <div className={styles.inputGroup}>
                  <label>额定功率</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={acPhysicalParams.ratedPower}
                      onChange={(e) => handleAcParamChange('ratedPower', Number(e.target.value))}
                    />
                    <span className={styles.units}>kW</span>
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>能效比</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={acPhysicalParams.energyRatio}
                      onChange={(e) => handleAcParamChange('energyRatio', Number(e.target.value))}
                    />
                  </div>
                </div>
              </div>
            </div>
          </CustomPane>

          {/* 环境物理参数 */}
          <CustomPane title="环境物理参数" button={
            <button
              className={`${styles.importButton} ${isPulse ? styles.pulse : ''}`}
              onClick={handleImportData}
              disabled={isCalculating}
            >
              {buttonText}
            </button>
          }>
            <div className={styles.inputSection}>
              <div className={styles.inputGrid}>
                <div className={styles.inputGroup}>
                  <label>等效热源</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={environmentParams.outdoorTemp}
                      onChange={(e) => handleEnvironmentParamChange('outdoorTemp', Number(e.target.value))}
                    />
                    <span className={styles.units}>℃</span>
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>等效热导</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={environmentParams.indoorTemp}
                      onChange={(e) => handleEnvironmentParamChange('indoorTemp', Number(e.target.value))}
                    />
                    <span className={styles.units}>℃</span>
                  </div>
                </div>
              </div>
            </div>
          </CustomPane>

          {/* 用户舒适度设定 */}
          <CustomPane title="用户舒适度设定" button={
            <button
              className={`${styles.importButton} ${isPulse ? styles.pulse : ''}`}
              onClick={handleImportData}
              disabled={isCalculating}
            >
              {buttonText}
            </button>
          }>
            <div className={styles.inputSection}>
              <div className={styles.inputGrid}>
                <div className={styles.inputGroup}>
                  <label>用户设定温度</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={comfortSettings.userSetTemp}
                      onChange={(e) => handleComfortSettingChange('userSetTemp', Number(e.target.value))}
                    />
                    <span className={styles.units}>℃</span>
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>最大可接受温差</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={comfortSettings.maxTempVariation}
                      onChange={(e) => handleComfortSettingChange('maxTempVariation', Number(e.target.value))}
                    />
                    <span className={styles.units}>℃</span>
                  </div>
                </div>
              </div>
            </div>
          </CustomPane>
        </div>

        {/* 右侧输出结果面板 */}
        <CustomPane title="输出结果">
        <div className={styles.chartPanel}>
          
            {/* 评估指标 */}
            <div className={styles.indicatorsGrid}>
              <div className={styles.indicatorItem}>
                <div className={styles.indicatorLabel}>备用容量</div>
                <div className={styles.indicatorValue}>
                  {evaluationResults.capacity.toFixed(1)}
                  <span className={styles.unit}>%</span>
                </div>
              </div>

              <div className={styles.indicatorItem}>
                <div className={styles.indicatorLabel}>响应时间</div>
                <div className={styles.indicatorValue}>
                  {evaluationResults.responseTime.toFixed(1)}
                  <span className={styles.unit}>min</span>
                </div>
              </div>

              <div className={styles.indicatorItem}>
                <div className={styles.indicatorLabel}>持续时间</div>
                <div className={styles.indicatorValue}>
                  {evaluationResults.maintainTime.toFixed(1)}
                  <span className={styles.unit}>min</span>
                </div>
              </div>

              <div className={styles.indicatorItem}>
                <div className={styles.indicatorLabel}>爬坡率</div>
                <div className={styles.indicatorValue}>
                  {evaluationResults.crawlRate.toFixed(1)}
                  <span className={styles.unit}>%</span>
                </div>
              </div>
            </div>

            {/* 图表区域 */}
            <div className={styles.chartsSection}>
              <div className={styles.chartContainer}>
                <div className={styles.chartTitle}>功率变化曲线</div>
                <div className={styles.chartContent} style={{height: px(300)}}>
                  
                  <ReactECharts
                    option={powerChangeOption}
                    style={{ height: '100%', width: '100%' }}
                    className="chart"
                  />
                </div>
              </div>

              <div className={styles.chartContainer}>
                <div className={styles.chartTitle}>室温变化曲线</div>
                <ReactECharts
                  option={temperatureChangeOption}
                  style={{ height: px(260), width: '100%' }}
                  className="chart"
                />
              </div>
            </div>
      
        </div>
        </CustomPane>
      </div>
    </div>
  );
};

export default FaultSettings;
