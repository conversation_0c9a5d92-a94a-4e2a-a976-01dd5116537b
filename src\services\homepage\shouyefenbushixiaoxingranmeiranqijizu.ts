// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/DistributedSmallCoalGasGen/delete */
export async function deleteUsingDELETE3(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE3Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/DistributedSmallCoalGasGen/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/DistributedSmallCoalGasGen/page */
export async function pageUsingGET1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGET1Params,
  options?: { [key: string]: any },
) {
  return request<API.PageDistributedSmallCoalGasGen>(
    '/masc/DistributedSmallCoalGasGen/page',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 单条插入 POST /masc/DistributedSmallCoalGasGen/save */
export async function saveUsingPOST3(
  body: API.DistributedSmallCoalGasGen,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/DistributedSmallCoalGasGen/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/DistributedSmallCoalGasGen/update */
export async function updateUsingPUT3(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT3Params,
  body: API.DistributedSmallCoalGasGen,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/DistributedSmallCoalGasGen/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/DistributedSmallCoalGasGen/upload */
export async function uploadUsingPOST3(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/DistributedSmallCoalGasGen/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
