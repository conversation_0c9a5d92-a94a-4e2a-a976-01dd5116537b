// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 查询所有机组编号 GET /masc/CoalGenQuoteCapacity/findAllGensetNumber */
export async function findAllGensetNumberUsingGET(options?: {
  [key: string]: any;
}) {
  return request<number[]>('/masc/CoalGenQuoteCapacity/findAllGensetNumber', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询一条（展示用）,quote报价集合,capacity报量集合 GET /masc/CoalGenQuoteCapacity/findByGensetNumber */
export async function findByGensetNumberUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.findByGensetNumberUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(
    '/masc/CoalGenQuoteCapacity/findByGensetNumber',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 表格上传 POST /masc/CoalGenQuoteCapacity/upload */
export async function uploadUsingPOST1(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/CoalGenQuoteCapacity/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
