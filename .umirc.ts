import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: '多时空协同的市场化电力系统风险防控辅助决策软件',
  },
  headScripts: [
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js'
  ],
  links: [
    {
      rel: 'stylesheet',
      href: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'
    }
  ],
  proxy: {
    '/masc': {
      //target: 'http://localhost:3002/',
      target: 'http://**************:9012',
      changeOrigin: true,
      // pathRewrite: { '^/masc': '' },
    },
  },
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    {
      name: '单体负荷预测',
      path: '/home',
      component: './NewHome',
      // hideInMenu: true,
    },

    {
      name: '单体负荷响应能力评估',
      path: '/control',
      component: './Control',
      // hideInMenu: true,
    },
    {
      name: '集群负荷响应能力评估',
      path: '/warning',
      component: './Warning',
      // hideInMenu: true,
     
    },
    
  ],
  npmClient: 'pnpm',
});
