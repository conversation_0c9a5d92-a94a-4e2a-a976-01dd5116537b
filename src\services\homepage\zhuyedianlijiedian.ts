// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/EleNode/delete */
export async function deleteUsingDELETE5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE5Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/EleNode/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/EleNode/page */
export async function pageUsingGET3(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGET3Params,
  options?: { [key: string]: any },
) {
  return request<API.PageEleNode>('/masc/EleNode/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/EleNode/save */
export async function saveUsingPOST5(
  body: API.EleNode,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/EleNode/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/EleNode/update */
export async function updateUsingPUT5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT5Params,
  body: API.EleNode,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/EleNode/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/EleNode/upload */
export async function uploadUsingPOST5(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/EleNode/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
