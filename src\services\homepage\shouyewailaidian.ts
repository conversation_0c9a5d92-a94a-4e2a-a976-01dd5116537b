// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/ExternalElectricity/delete */
export async function deleteUsingDELETE7(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE7Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/ExternalElectricity/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/ExternalElectricity/page */
export async function pageUsingGET5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGET5Params,
  options?: { [key: string]: any },
) {
  return request<API.PageExternalElectricity>(
    '/masc/ExternalElectricity/page',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 单条插入 POST /masc/ExternalElectricity/save */
export async function saveUsingPOST7(
  body: API.ExternalElectricity,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/ExternalElectricity/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/ExternalElectricity/update */
export async function updateUsingPUT7(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT7Params,
  body: API.ExternalElectricity,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/ExternalElectricity/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/ExternalElectricity/upload */
export async function uploadUsingPOST7(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/ExternalElectricity/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
