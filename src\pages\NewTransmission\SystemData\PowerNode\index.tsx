import ChartTitle from '@/components/ui/chartTitle';
import _interface from '@/services/interface';
import px from '@/utils/px';
import {
  Button,
  Empty,
  Form,
  Input,
  Popconfirm,
  Space,
  Table,
  Upload,
  message,
} from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.sass';

const { uploadUsingPost2, pageUsingGet1, deleteUsingDelete3, updateUsingPut3 } =
  _interface.dianlixitongjiedian;

const EditableCell = ({
  editing,
  dataIndex,
  title,
  record,
  children,
  ...restProps
}: any) => {
  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[{ required: true, message: `请输入${title}!` }]}
          initialValue={record[dataIndex]}
        >
          <Input />
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const Index = () => {
  const [dataSource, setDataSource] = useState<API.EleNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingKey, setEditingKey] = useState<string>('');
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 8,
    total: 0,
  });

  const isEditing = (record: any) => record.id === editingKey;
  const fetchData = async (page = 1) => {
    try {
      setLoading(true);
      const response = (await pageUsingGet1({
        current: page,
        size: pagination.pageSize,
      })) as ResponseData<API.PageEleNode>;

      if (response.code === 1000 && response.data?.records) {
        setDataSource(response.data.records);
        setPagination({
          ...pagination,
          current: page,
          total: response.data.total || 0,
        });
      }
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (record: API.EleNode) => {
    form.setFieldsValue({
      busI: record.busI,
      type: record.type,
      pd: record.pd,
      qd: record.qd,
      gs: record.gs,
      bs: record.bs,
      area: record.area,
      vm: record.vm,
      va: record.va,
      baseKv: record.baseKv,
      zone: record.zone,
      vmax: record.vmax,
      vmin: record.vmin,
    });
    setEditingKey(record.id);
  };

  const handleCancel = () => {
    setEditingKey('');
  };

  const handleSave = async (record: API.EleNode) => {
    try {
      const row = await form.validateFields();
      await updateUsingPut3({ id: record.id }, { ...record, ...row });
      message.success('保存成功');
      setEditingKey('');
      fetchData();
    } catch (error) {
      message.error('保存失败');
      console.error('Save failed:', error);
    }
  };

  const handleDelete = async (record: API.EleNode) => {
    try {
      await deleteUsingDelete3({ id: record.id });
      message.success('删除成功');
      fetchData();
    } catch (error) {
      message.error('删除失败');
      console.error('Delete failed:', error);
    }
  };

  const columns = [
    {
      title: '节点编号',
      dataIndex: 'busI',
      key: 'busI',
      editable: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      editable: true,
    },
    {
      title: 'Pd',
      dataIndex: 'pd',
      key: 'pd',
      editable: true,
    },
    {
      title: 'Qd',
      dataIndex: 'qd',
      key: 'qd',
      editable: true,
    },
    {
      title: 'Gs',
      dataIndex: 'gs',
      key: 'gs',
      editable: true,
    },
    {
      title: 'Bs',
      dataIndex: 'bs',
      key: 'bs',
      editable: true,
    },
    {
      title: '区域',
      dataIndex: 'area',
      key: 'area',
      editable: true,
    },
    {
      title: 'Vm',
      dataIndex: 'vm',
      key: 'vm',
      editable: true,
    },
    {
      title: 'Va',
      dataIndex: 'va',
      key: 'va',
      editable: true,
    },
    {
      title: '基准电压',
      dataIndex: 'baseKv',
      key: 'baseKv',
      editable: true,
    },
    {
      title: '分区',
      dataIndex: 'zone',
      key: 'zone',
      editable: true,
    },
    {
      title: '最大电压',
      dataIndex: 'vmax',
      key: 'vmax',
      editable: true,
    },
    {
      title: '最小电压',
      dataIndex: 'vmin',
      key: 'vmin',
      editable: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_: any, record: API.EleNode) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button type="link" onClick={() => handleSave(record)}>
              保存
            </Button>
            <Button type="link" onClick={handleCancel}>
              取消
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              type="link"
              disabled={editingKey !== ''}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这条记录吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    fetchData();
  }, []);

  const customUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      await uploadUsingPost2(formData as any);
      onSuccess('OK');
      await fetchData();
    } catch (error) {
      onError(error);
      console.error('Upload failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.systemData}>
      <div className={styles.table}>
        <div className={styles.chartTitle}>
          <ChartTitle
            title="节点"
            width={px(600)}
            height={px(37)}
            right={
              <Upload customRequest={customUpload}>
                <Button>上传</Button>
              </Upload>
            }
          />
        </div>
        <div style={{ marginTop: px(20) }}>
          {dataSource.length > 0 ? (
            <Form form={form} component={false}>
              <Table
                components={{
                  body: {
                    cell: EditableCell,
                  },
                }}
                columns={columns.map((col) => {
                  if (!col.editable) {
                    return col;
                  }
                  return {
                    ...col,
                    onCell: (record: API.EleNode) => ({
                      record,
                      dataIndex: col.dataIndex,
                      title: col.title,
                      editing: isEditing(record),
                    }),
                  };
                })}
                dataSource={dataSource}
                loading={loading}
                rowKey="id"
                pagination={{
                  ...pagination,
                  onChange: (page) => fetchData(page),
                }}
              />
            </Form>
          ) : (
            <Empty description="没有数据，请上传数据" />
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
