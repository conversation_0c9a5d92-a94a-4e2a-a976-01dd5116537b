import React from 'react';
import styles from './index.module.sass';

interface MenuItem {
  key: string;
  label: string;
  children?: MenuItem[];
}

type HierarchicalSidebarProps = {
  menu: MenuItem[];
  activeId: string;
  onChange: (key: string) => void;
};

const HierarchicalSidebar: React.FC<HierarchicalSidebarProps> = ({
  menu,
  activeId,
  onChange,
}) => {
  const handleClick = (key: string) => {
    onChange(key);
  };

  const renderMenuItem = (item: MenuItem) => {
    const isActive = item.key === activeId;

    if (item.children) {
      return (
        <li key={item.key} className={styles.parentItem}>
          <div className={styles.parentLabel}>{item.label}</div>
          <ul className={styles.childList}>
            {item.children.map((child) => renderMenuItem(child))}
          </ul>
        </li>
      );
    }

    return (
      <li
        key={item.key}
        className={`${styles.childItem} ${isActive ? styles.active : ''}`}
        onClick={() => handleClick(item.key)}
      >
        {item.label}
      </li>
    );
  };

  return (
    <div className={styles.sidebarContainer}>
      <div className={styles.sidebar}>
        <ul className={styles.menuList}>
          {menu.map((item) => renderMenuItem(item))}
        </ul>
      </div>
      <div className={styles.divider}></div>
    </div>
  );
};

export default HierarchicalSidebar;
