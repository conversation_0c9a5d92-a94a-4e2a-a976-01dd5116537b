import px from '@/utils/px';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
interface LineChartProps {
  title?: string; // 图表标题
  xAxisData?: any[]; // x轴数据
  yAxisData?: number[]; // y轴数据
  xAxisName?: string; // x轴单位/名称
  yAxisName?: string; // y轴单位/名称
  legendName?: string; // 图例名称
  color?: string; // 线条颜色
  height?: number; // 图表高度
}

const LineChart = ({
  title,
  xAxisData = ['1', '2', '3', '4', '5', '6', '7'],
  yAxisData = [820, 932, 901, 934, 1290, 1330, 1320],
  xAxisName = '',
  yAxisName = '',
  legendName = '',
  color = '#6affeb',
  height = px(400),
}: LineChartProps) => {
  const chartRef = useRef(null);

  useEffect(() => {
    if (chartRef.current) {
      const myChart = echarts.init(chartRef.current, 'dark');

      const option = {
        backgroundColor: 'transparent',
        title: {
          text: title,
          textStyle: {
            color: '#fff',
          },
          left: 'center',
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          data: [legendName],
          textStyle: {
            color: '#fff',
          },
          top: 30,
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          name: xAxisName,
          nameLocation: 'middle',
          nameGap: 30,
          axisLabel: {
            color: '#fff',
          },
        },
        yAxis: {
          type: 'value',
          name: yAxisName,
          nameLocation: 'middle',
          nameGap: 40,
          splitLine: {
            lineStyle: {
              color: 'rgba(176, 215, 255, 0.25)',
              type: 'dashed',
            },
          },
          axisLabel: {
            color: '#fff',
          },
        },
        series: [
          {
            name: legendName,
            data: yAxisData,
            type: 'line',
            smooth: false,
            color: color,
            lineStyle: {
              width: 3,
            },
          },
        ],
        grid: {
          left: '5%',
          right: '5%',
          bottom: '10%',
          top: '15%',
          containLabel: true,
        },
      };

      myChart.setOption(option);

      // 响应窗口大小变化
      window.addEventListener('resize', () => myChart.resize());

      return () => {
        myChart.dispose();
        window.removeEventListener('resize', () => myChart.resize());
      };
    }
  }, [title, xAxisData, yAxisData, xAxisName, yAxisName, legendName, color]);

  return <div ref={chartRef} style={{ width: '100%', height: height }}></div>;
};

export default LineChart;
