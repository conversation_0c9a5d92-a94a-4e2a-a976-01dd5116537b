// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 导出数据 GET /masc/PostDisasterFlexibleLoadPlan/download */
export async function downloadUsingGET1(options?: { [key: string]: any }) {
  return request<any>('/masc/PostDisasterFlexibleLoadPlan/download', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询全部数据 GET /masc/PostDisasterFlexibleLoadPlan/query */
export async function queryUsingGET1(options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    '/masc/PostDisasterFlexibleLoadPlan/query',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 表格上传 POST /masc/PostDisasterFlexibleLoadPlan/upload */
export async function uploadUsingPOST17(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/PostDisasterFlexibleLoadPlan/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
