import px from '@/utils/px';
import AMapLoader from '@amap/amap-jsapi-loader';
import { useEffect } from 'react';
import styles from './index.sass';

import locationData from '@/api/outnode/del.json'; // 添加这行导入JSON文件
export const MapIndex = () => {
  let map = null;

  useEffect(() => {
    // 设置密钥
    window._AMapSecurityConfig = {
      securityJsCode: '52b4cf4f44340f10fedbc716b1c5e5f1',
    };
    AMapLoader.load({
      key: '2ce45da06e505bdc29e14b5f0d54b153', // 申请好的Web端开发者Key,首次调用 load 时必填
      version: '2.0', // 指定要加载的 JSAPI 的版本,缺省时默认为 1.4.15
      plugins: [
        'AMap.ToolBar',
        'AMap.ControlBar',
        'AMap.CustomLayer',
        'AMap.InfoWindow',
        'AMap.DistrictSearch',
        'AMap.Polyline',
        'AMap.Polygon',
      ], // 需要使用的的插件列表,如比例尺'AMap.Scale'等
      AMapUI: {
        version: '1.1',
        plugins: ['overlay/SimpleMarker'],
      },
    })
      .then((AMap) => {
        const res = { data: locationData.data };

        let data = res.data;
        let nodeList = data.nodePositionList;
        map = new AMap.Map('container', {
          zoom: 7.8,
          //zoomEnable: false,			//伸缩
          //dragEnable: false,		//拖动
          expandZoomRange: true,
          center: [120.35, 29.52], //杭州
          // center: [120.75, 30.08],
          viewMode: '2D',
          pitch: 60,
          mapStyle: 'amap://styles/blue',
          features: ['bg', 'point'],
          logoPosition: 'bottom',
          logoOffset: new AMap.Pixel(75, 20),
        });
        new AMap.DistrictSearch({
          subdistrict: 0,
          extensions: 'all',
          level: 'province', //查询行政级别为 省
        }).search('浙江省', function (status, result) {
          // 外多边形坐标数组和内多边形坐标数组
          let outer = [
            new AMap.LngLat(-360, 90, true),
            new AMap.LngLat(-360, -90, true),
            new AMap.LngLat(360, -90, true),
            new AMap.LngLat(360, 90, true),
          ];
          let holes = [];
          if (result.districtList.length > 0) {
            holes = result.districtList[0].boundaries;
          }

          let pathArray = [outer];
          pathArray.push.apply(pathArray, holes);
          let polygon = new AMap.Polygon({
            pathL: pathArray,
            //线条颜色，使用16进制颜色代码赋值。默认值为#006600
            strokeColor: 'grey',
            strokeWeight: 1,
            //轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
            strokeOpacity: 1,
            //多边形填充颜色，使用16进制颜色代码赋值，如：#FFAA00
            //fillColor: 'rgba(0,0,0)',
            fillColor: '#000',
            //多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9
            fillOpacity: 1,
            //轮廓线样式，实线:solid，虚线:dashed
            strokeStyle: 'solid',
            /*勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在    
                              ie9+浏览器有效 取值： 
                              实线：[0,0,0] 
                              虚线：[10,10] ，[10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线
                              点画线：[10,2,10]， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实 
                              线和10个像素的空白 （如此反复）组成的虚线*/
            strokeDasharray: [10, 2, 10],
          });
          polygon.setPath(pathArray);
          map.add(polygon);
        });

        map.on('complete', function () {
          let viewData = {
            '2D': 3e3,
          };
          let curViewMode = map.getViewMode_();

          // 创建 AMap.LabelsLayer 图层
          let layer = new AMap.LabelsLayer({
            zooms: [3, 20],
            zIndex: 111,
            // 关闭标注避让，默认为开启，v1.4.15 新增属性
            animation: false,
            // 关闭标注淡入动画，默认为开启，v1.4.15 新增属性
            collision: false,
          });

          // 将图层添加到地图
          map.add(layer);

          let markers = [];

          for (let i = 0; i < nodeList.length; i++) {
            let curPosition = nodeList[i].position;

            let icon = {
              type: 'image',
              image: require('@/assets/img/' +
                nodeList[i].gensetAndNode +
                '.png'),
              size: [36, 40],
              anchor: 'center',
              angel: 0,
              retina: true,
            };

            let curData = {
              position: curPosition,
              icon,
            };
            //console.log(curData);
            let labelMarker = new AMap.LabelMarker(curData);

            // 事件
            labelMarker.on('mouseover', function (e) {
              //console.log(e);
              let position = e.data.data && e.data.data.position;

              let gensetOutput = '';
              for (let node of nodeList) {
                if (position === node.position) {
                  if (!gensetOutput) {
                    gensetOutput =
                      gensetOutput +
                      node.gensetName +
                      '（' +
                      node.output +
                      'MW）';
                  } else {
                    gensetOutput =
                      gensetOutput +
                      '，' +
                      node.gensetName +
                      '（' +
                      node.output +
                      'MW）';
                  }
                  //若节点有机组
                  if (node.gensetName) {
                    normalMarker.setContent(
                      '<div class="amap-info-window">' +
                        '<div class="nodeInfoTitle">节点信息</div>' +
                        '<div>' +
                        '节点名称：' +
                        node.realName +
                        '</div>' +
                        '<div>' +
                        '节点编号：' +
                        node.nodeName +
                        '</div>' +
                        '<div>' +
                        '节点类型：' +
                        node.type +
                        '</div>' +
                        '<div>' +
                        '发电量：' +
                        gensetOutput +
                        '</div>' +
                        '<div>' +
                        '有功负荷：' +
                        node.activeLoad +
                        'MW</div>' +
                        '<div>' +
                        '无功负荷：' +
                        node.reactiveLoad +
                        'MW</div>' +
                        '<div>' +
                        '节点电价：' +
                        node.eleprice +
                        '元/MWh</div>' +
                        '<div class="amap-info-sharp"></div>' +
                        '</div>',
                    );
                  } else if (node.type === '特高压直流馈入点') {
                    normalMarker.setContent(
                      '<div class="amap-info-window">' +
                        '<div class="nodeInfoTitle">节点信息</div>' +
                        '<div>' +
                        '节点名称：' +
                        node.realName +
                        '</div>' +
                        '<div>' +
                        '节点编号：' +
                        node.nodeName +
                        '</div>' +
                        '<div>' +
                        '节点类型：' +
                        node.type +
                        '</div>' +
                        '<div>接入容量：4500MW</div>' +
                        '<div>' +
                        '节点电价：' +
                        node.eleprice +
                        '元/MWh</div>' +
                        '<div class="amap-info-sharp"></div>' +
                        '</div>',
                    );
                  } else {
                    normalMarker.setContent(
                      '<div class="amap-info-window">' +
                        '<div class="nodeInfoTitle">节点信息</div>' +
                        '<div>' +
                        '节点名称：' +
                        node.realName +
                        '</div>' +
                        '<div>' +
                        '节点编号：' +
                        node.nodeName +
                        '</div>' +
                        '<div>' +
                        '节点类型：' +
                        node.type +
                        '</div>' +
                        '<div>' +
                        '有功负荷：' +
                        node.activeLoad +
                        'MW</div>' +
                        '<div>' +
                        '无功负荷：' +
                        node.reactiveLoad +
                        'MW</div>' +
                        '<div>' +
                        '节点电价：' +
                        node.eleprice +
                        '元/MWh</div>' +
                        '<div class="amap-info-sharp"></div>' +
                        '</div>',
                    );
                  }
                  normalMarker.setPosition(position);
                  map.add(normalMarker);
                }
              }
            });

            labelMarker.on('mouseout', function () {
              map.remove(normalMarker);
            });

            markers.push(labelMarker);
          }
          //console.log(markers);
          // 一次性将海量点添加到图层
          layer.add(markers);

          // 普通点
          let normalMarker = new AMap.Marker({
            offset: new AMap.Pixel(-75, -40),
          });

          AMapUI.load(
            ['ui/misc/PathSimplifier', 'lib/$'],
            function (PathSimplifier, $) {
              if (!PathSimplifier.supportCanvas) {
                alert('当前环境不支持 Canvas！');
                return;
              }

              //不过载
              let pathSimplifierIns = new PathSimplifier({
                zIndex: 110, //线路图层在点图层之上，在空白覆盖之下
                autoSetFitView: false,
                map: map, //所属的地图实例

                getPath: function (pathData, pathIndex) {
                  return pathData.path;
                },
                getHoverTitle: function (pathData, pathIndex, pointIndex) {
                  /*if(pointIndex >= 0) {
                                        //point 
                                        return pathData.name + '，点：' + pointIndex + '/' + pathData.path.length;
                                    }
    
                                    return pathData.name + '，点数量' + pathData.path.length;*/
                  if (pathData.num > 1) {
                    return (
                      '<div>双回线<br>线路编号：' +
                      pathData.name +
                      ' <br>首端节点：' +
                      pathData.firstNodeName +
                      ' <br>末端节点：' +
                      pathData.endNodeName +
                      ' <br>线路容量：' +
                      pathData.capacity +
                      'MW' +
                      ' <br>实时功率：' +
                      pathData.power +
                      'MW' +
                      ' <br>利用率：' +
                      pathData.utilization +
                      '</div>'
                    );
                  } else {
                    return (
                      '<div>单回线<br>线路编号：' +
                      pathData.name +
                      ' <br>首端节点：' +
                      pathData.firstNodeName +
                      ' <br>末端节点：' +
                      pathData.endNodeName +
                      ' <br>线路容量：' +
                      pathData.capacity +
                      'MW' +
                      ' <br>实时功率：' +
                      pathData.power +
                      'MW' +
                      ' <br>利用率：' +
                      pathData.utilization +
                      '</div>'
                    );
                  }
                },
                getPathStyle: function (pathData, pathIndex) {
                  if (pathData.isHighlighted) {
                    return {
                      strokeStyle: '#ff0000', // 红色
                      lineWidth: 2,
                      dirArrowStyle: false,
                    };
                  }
                  return null; // 使用默认样式
                },
                renderOptions: {
                  pathLineStyle: {
                    strokeStyle: '#29d206', // 正常路径的颜色
                    lineWidth: 2,
                    dirArrowStyle: true,
                  },
                  pathLineHoverStyle: {
                    // 添加hover样式
                    strokeStyle: '#1890ff', // 蓝色
                    lineWidth: 3,
                    borderWidth: 1,
                    borderStyle: '#cccccc',
                    dirArrowStyle: false,
                  },
                  renderAllPointsIfNumberBelow: 100, //绘制路线节点，如不需要可设置为-1
                  pathNavigatorStyle: {
                    initRotateDegree: 0,
                    width: 16,
                    height: 16,
                    autoRotate: true,
                    lineJoin: 'round',
                    content: 'defaultPathNavigator',
                    //fillStyle: '#c11534',
                    fillStyle: '#29d206',
                    strokeStyle: '#eee', //'#eeeeee',
                    lineWidth: 1,
                    pathLinePassedStyle: {
                      lineWidth: 2,
                      strokeStyle: '#29d206',
                      borderWidth: 1,
                      borderStyle: '#eeeeee',
                      dirArrowStyle: {
                        stepSpace: 15,
                        strokeStyle: 'red',
                      },
                    },
                  },
                },
              });

              //过载
              let overloadPathSimplifierIns = new PathSimplifier({
                zIndex: 112, //线路图层在点图层之上，在空白覆盖之下
                autoSetFitView: false,
                map: map, //所属的地图实例
                getPath: function (pathData) {
                  return pathData.path;
                },
                getHoverTitle: function (pathData, pathIndex, pointIndex) {
                  if (pathData.num > 1) {
                    return (
                      '<div>双回线<br>线路编号：' +
                      pathData.name +
                      ' <br>首端节点：' +
                      pathData.firstNodeName +
                      ' <br>末端节点：' +
                      pathData.endNodeName +
                      ' <br>线路容量：' +
                      pathData.capacity +
                      'MW' +
                      ' <br>实时功率：' +
                      pathData.power +
                      'MW' +
                      ' <br>利用率：' +
                      pathData.utilization +
                      'MW</div>'
                    );
                  } else {
                    return (
                      '<div>单回线<br>线路编号：' +
                      pathData.name +
                      ' <br>首端节点：' +
                      pathData.firstNodeName +
                      ' <br>末端节点：' +
                      pathData.endNodeName +
                      ' <br>线路���量：' +
                      pathData.capacity +
                      'MW' +
                      ' <br>实时功率：' +
                      pathData.power +
                      'MW' +
                      ' <br>利用率：' +
                      pathData.utilization +
                      'MW</div>'
                    );
                  }
                },
                renderOptions: {
                  renderAllPointsIfNumberBelow: 100, //绘制路线节点，如不需要可设置为-1
                  pathNavigatorStyle: {
                    initRotateDegree: 0,
                    width: 16,
                    height: 16,
                    autoRotate: true,
                    lineJoin: 'round',
                    content: 'defaultPathNavigator',
                    fillStyle: '#c11534',
                    //fillStyle: '#29d206',
                    strokeStyle: '#eee', //'#eeeeee',
                    lineWidth: 1,
                    pathLinePassedStyle: {
                      lineWidth: 2,
                      strokeStyle: '#c11534',
                      borderWidth: 1,
                      borderStyle: '#eeeeee',
                      dirArrowStyle: {
                        stepSpace: 15,
                        strokeStyle: 'red',
                      },
                    },
                  },
                },
              });

              //window.pathSimplifierIns = pathSimplifierIns;

              //设置数据
              let linePositionList = [];
              let overloadLinePositionList = [];
              for (let item of data.linePositionList) {
                //线路实时功率达到容量的80%判定为过载
                /*if(item.lineName == "l62") {
                                    overloadLinePositionList.push(item);
                                } else {
                                    linePositionList.push(item);
                                }*/
                if (Math.abs(item.pf / item.capacity) > 0.85) {
                  overloadLinePositionList.push(item);
                } else {
                  linePositionList.push(item);
                }
              }

              //console.log(linePositionList);
              let pathData = [];
              for (let linePosition of linePositionList) {
                //判断单双回线
                let num = 0;
                for (let item of data.linePositionList) {
                  if (
                    item.firstNode === linePosition.firstNode &&
                    item.endNode === linePosition.endNode
                  ) {
                    num++;
                  }
                }
                //坐标由字符串类型转为数字类型
                let firstNode = linePosition.firstNode.split(',').map(Number);
                let endNode = linePosition.endNode.split(',').map(Number);
                let path = [];
                let utilization = Math.abs(
                  (linePosition.pf / linePosition.capacity) * 100,
                );
                utilization = utilization.toFixed(2);
                path.push(firstNode);
                path.push(endNode);
                pathData.push({
                  name: linePosition.lineName,
                  capacity: linePosition.capacity,
                  power: linePosition.pf,
                  utilization: utilization + '%',
                  path: path,
                  num: num,
                  firstNodeName: linePosition.firstNodeName,
                  endNodeName: linePosition.endNodeName,
                });
              }
              pathSimplifierIns.setData(pathData);

              //过载
              let overloadPathData = [];
              for (let linePosition of overloadLinePositionList) {
                //判断单双回线
                let num = 0;
                for (let item of data.linePositionList) {
                  if (
                    item.firstNode === linePosition.firstNode &&
                    item.endNode === linePosition.endNode
                  ) {
                    num++;
                  }
                }
                //坐标由字符串类型转为数字类型
                let firstNode = linePosition.firstNode.split(',').map(Number);
                let endNode = linePosition.endNode.split(',').map(Number);
                let path = [];
                let utilization = Math.abs(
                  (linePosition.pf / linePosition.capacity) * 100,
                );
                utilization = utilization.toFixed(2);
                path.push(firstNode);
                path.push(endNode);
                overloadPathData.push({
                  name: linePosition.lineName,
                  capacity: linePosition.capacity,
                  power: linePosition.pf,
                  utilization: utilization + '%',
                  path: path,
                  num: num,
                  firstNodeName: linePosition.firstNodeName,
                  endNodeName: linePosition.endNodeName,
                });
              }
              overloadPathSimplifierIns.setData(overloadPathData);

              /*pathSimplifierIns.setData([{
                                name: 'l1',
                                path: [
                                    [120.295727,30.357987],
                                    [120.098004,30.450074]
                                ]
                            }]);*/

              //对第一条线路（即索引 0）创建一个巡航器
              /*var navg1 = pathSimplifierIns.createPathNavigator(0, {
                                loop: true, //循环播放
                                speed: 10000 //巡航速度，单位千米/小时
                            });
    
                            navg1.start();*/

              //循环创建巡航器
              //   for (let i = 0; i < linePositionList.length; i++) {
              //     pathSimplifierIns
              //       .createPathNavigator(i, {
              //         loop: true, //循环播放
              //         speed: 50000, //巡航速度，单位千米/小时
              //       })
              //       .start();
              //   }

              //   //循环创建巡航器
              //   for (let i = 0; i < overloadLinePositionList.length; i++) {
              //     overloadPathSimplifierIns
              //       .createPathNavigator(i, {
              //         loop: true, //循环播放
              //         speed: 50000, //巡航速度，单位千米/小时
              //       })
              //       .start();
              //   }

              /*function openInfo(point) {
                                //构建信息窗体中显示的内容
                                var info = [];
    
                                info.push("<div>线路阻塞</div>");
                                infoWindow = new AMap.InfoWindow({
                                    content: info.join("<br/>") //使用默认信息窗体框样式，显示信息内容
                                });
                                infoWindow.open(map, point);
    
                            }*/

              for (let item of overloadLinePositionList) {
                let pointList = [];
                pointList.push(item.firstNode);
                pointList.push(item.endNode);
                let point = getPointsCenter(pointList);

                let marker = new AMap.Marker({
                  position: point,
                  icon: require('@/assets/img/过载报警.png'),
                  anchor: 'top-center',
                });

                marker.setMap(map);

                // 设置label标签
                // label默认蓝框白底左上角显示，样式className为：amap-marker-label
                // marker.setLabel({
                //     //offset: new AMap.Pixel(20, 20), //设置文本标注偏移量
                //     content: "<div class='overloadInfo'>线路阻塞</div>", //设置文本标注内容
                //     direction: 'right', //设置文本标注方位
                // })
              }
            },
          );

          /*var mapViewElement = document.getElementById('mapView');
                        var massNumberElement = document.getElementById('massNumber');
                        mapViewElement.innerHTML = curViewMode;
                        massNumberElement.innerHTML = viewData[curViewMode];*/
        });
        function getPointsCenter(points) {
          let point_num = points.length; //坐标点个数
          let X = 0,
            Y = 0,
            Z = 0;
          for (let i = 0; i < points.length; i++) {
            if (points[i] === '') {
              continue;
            }
            let point = points[i].split(',');
            let lat, lng, x, y, z;
            lat = (parseFloat(point[1]) * Math.PI) / 180;
            lng = (parseFloat(point[0]) * Math.PI) / 180;
            x = Math.cos(lat) * Math.cos(lng);
            y = Math.cos(lat) * Math.sin(lng);
            z = Math.sin(lat);
            X += x;
            Y += y;
            Z += z;
          }
          X = X / point_num;
          Y = Y / point_num;
          Z = Z / point_num;

          let tmp_lng = Math.atan2(Y, X);
          let tmp_lat = Math.atan2(Z, Math.sqrt(X * X + Y * Y));

          return [(tmp_lng * 180) / Math.PI, (tmp_lat * 180) / Math.PI];
        }
      })
      .catch(() => {});

    return () => {
      if (map) {
        map.destroy();
      }
    };
  }, []);

  return (
    <div
      id="container"
      className={styles.main}
      style={{ height: '100%', borderRadius: px(20) }}
    ></div>
  );
};
