// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/CoalPrice/delete */
export async function deleteUsingDELETE2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE2Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/CoalPrice/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/CoalPrice/save */
export async function saveUsingPOST2(
  body: API.CoalPrice,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/CoalPrice/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询一个月 GET /masc/CoalPrice/selectOneMonth */
export async function selectOneMonthUsingGET1(options?: {
  [key: string]: any;
}) {
  return request<number[]>('/masc/CoalPrice/selectOneMonth', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/CoalPrice/update */
export async function updateUsingPUT2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT2Params,
  body: API.CoalPrice,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/CoalPrice/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/CoalPrice/upload */
export async function uploadUsingPOST2(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/CoalPrice/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
