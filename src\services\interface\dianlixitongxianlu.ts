// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/ElePipe/delete */
export async function deleteUsingDelete4(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE4Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/ElePipe/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/ElePipe/page */
export async function pageUsingGet2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGET2Params,
  options?: { [key: string]: any },
) {
  return request<API.PageElePipe>('/masc/ElePipe/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/ElePipe/save */
export async function saveUsingPost4(
  body: API.ElePipe,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/ElePipe/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/ElePipe/update */
export async function updateUsingPut4(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT4Params,
  body: API.ElePipe,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/ElePipe/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/ElePipe/upload */
export async function uploadUsingPost3(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/ElePipe/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
