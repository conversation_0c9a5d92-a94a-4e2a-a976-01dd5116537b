// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/GasGensetCoefficient/delete */
export async function deleteUsingDelete6(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE6Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasGensetCoefficient/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/GasGensetCoefficient/save */
export async function saveUsingPost6(
  body: API.GasGensetCoefficient,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasGensetCoefficient/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询所有燃气机组耗气系数成本曲线 GET /masc/GasGensetCoefficient/selectAll */
export async function selectAllUsingGet(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/masc/GasGensetCoefficient/selectAll', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/GasGensetCoefficient/update */
export async function updateUsingPut6(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT6Params,
  body: API.GasGensetCoefficient,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasGensetCoefficient/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/GasGensetCoefficient/upload */
export async function uploadUsingPost4(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasGensetCoefficient/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
