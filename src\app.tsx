// 运行时配置
import { RunTimeLayoutConfig, history } from '@umijs/max';
import { ConfigProvider, theme } from 'antd';
import classNames from 'classnames';
import styles from './app.sass';
import bg from './assets/newlayout/bg.png';
import px from './utils/px.js';
const theme2 = {
  components: {
    Button: {
      colorBgContainer: 'transparent',
      colorBorder: '#486B95',
      colorText: '#D8F0FF',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      colorBgContainerDisabled: 'transparent',
      colorTextDisabled: 'rgba(72, 107, 149, 0.5)', // 稍微淡化禁用状态的文字颜色
    },
    Table: {
      colorBgContainer: 'transparent',
      headerBg: 'rgb(18,41,79)',
      headerColor: 'white', // 设置表头文字颜色
      colorText: 'white',
      // borderColor: 'transparent', // 设置边框颜色为透明
      // cellBorderColor: 'transparent', // 设置单元格边框颜色为透明
      // headerBorderColor: 'transparent', // 设置表头边框颜色为透明
      rowHeight: 20, // 设置行高为 50px
    },
    Pagination: {
      colorBgContainer: 'transparent',
      colorBorder: '#486B95', // 使用与 Button 相同的边框颜色
      colorText: '#D8F0FF', // 使用与 Button 相同的文字颜色
      colorPrimary: '#486B95', // 当前页码的颜色
      colorPrimaryHover: '#5881B3', // 悬停时的颜色
      colorBgContainerDisabled: 'transparent',
      colorTextDisabled: 'rgba(72, 107, 149, 0.5)',
    },
  },
  algorithm: theme.darkAlgorithm,
};
// 全局初始化数据配置，用于 Layout 用户信息和权限初始化
// 更多信息见文档：https://umijs.org/docs/api/runtime-config#getinitialstate

export const layout: RunTimeLayoutConfig = () => {
  return {
    layout: 'top',
    fixedHeader: false,
    headerRender: (props: any) => {
      const { menuData } = props;
      const clearMenu = menuData.filter(
        (item: any) => item.path !== '/' && item.hideInMenu !== true,
      );
      return (
        <div className={styles.layout}>
          <div className={styles.title}>
          负荷资源响应能力预测平台
          </div>
          <div className={styles.menu}>
            {clearMenu.map((item: any) => (
              <div
                key={item.path}
                onClick={() => {
                  history.push(item.path);
                }}
                className={classNames(
                  styles.menuItem,
                  item.path === props.matchMenuKeys[0]
                    ? styles.menuItemActive
                    : null,
                )}
              >
                {item.name}
              </div>
            ))}
          </div>
        </div>
      );
    },
    contentStyle: {
      backgroundImage: `url(${bg})`,
      backgroundSize: '100% 100%',
      backgroundRepeat: 'no-repeat',
      paddingTop: px(10),
      height: (window as any).pageHeight,
      width: (window as any).pageWidth,
    },
  };
};

export function rootContainer(container: React.ReactNode) {
  return (
    <div
      style={{
        width: '100vw',
        height: '100vh',
        background: 'black',
        backgroundSize: '100% 100%',
        padding: 0,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <div
        style={{
          width: window.pageWidth,
          height: window.pageHeight,
        }}
        className={styles.outClass}
        id="outClass"
      >
        <ConfigProvider theme={theme2}>{container}</ConfigProvider>
      </div>
    </div>
  );
}
