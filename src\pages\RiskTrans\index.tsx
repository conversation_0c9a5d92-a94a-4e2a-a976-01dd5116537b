import CurveChart from '@/components/CurveChart';
import CustomInput from '@/components/CustomInput';
import CustomPane from '@/components/CustomPane';
import CustomTable from '@/components/CustomTable';
import {
  callMainFunctionUsingGET,
  getSystemPriceUsingGET,
} from '@/services/interface/liangjiafengxianchuandao';
import px from '@/utils/px';
import { Form, message, Spin } from 'antd';
import React, { useState } from 'react';
import Indicator from './Indicator';
import MultiLineChart from './MultiLineChart';
import styles from './page.module.sass';

// 将 ResultCharts 组件移到父组件外部
const ResultCharts = React.memo(
  ({
    priceChartData,
    gasPrice,
  }: {
    priceChartData: number[][];
    gasPrice: number[][];
  }) => {
    return (
      <div className={styles.chartContainer}>
        <div className={styles.resultchart}>
          <MultiLineChart
            data={priceChartData}
            yAxisName="电价(元/kWh)"
            legendNames={[
              '1.0倍煤价成本',
              '1.1倍煤价成本',
              '1.2倍煤价成本',
              '1.3倍煤价成本',
            ]}
            title="出清煤价曲线"
            height={px(600)}
          />
        </div>
        <div className={styles.resultchart}>
          <MultiLineChart
            data={gasPrice}
            yAxisName="电价(元/kWh)"
            legendNames={[
              '1.0倍气价成本',
              '1.1倍气价成本',
              '1.2倍气价成本',
              '1.3倍气价成本',
            ]}
            title="出清气价曲线"
            height={px(600)}
          />
        </div>
      </div>
    );
  },
);

export default function Page() {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const originalLoadData = [
    3972.877695, 3844.72035, 3716.563005, 3203.933625, 3332.09097, 3075.77628,
    3075.77628, 3075.77628, 2563.1469, 3075.77628, 10124.43026, 11277.84636,
    11534.16105, 10252.5876, 6664.18194, 5510.765835, 8714.69946, 10252.5876,
    12174.94778, 12046.79043, 10765.21698, 7689.4407, 5382.60849, 3844.72035,
  ];
  const [chartData, setChartData] = useState(originalLoadData); // 添加状态管理图表数据

  // 监听表单值变化
  const handleValuesChange = (changedValues: any) => {
    if ('kc' in changedValues) {
      const kcValue = Number(changedValues.kc);
      if (kcValue && !isNaN(kcValue)) {
        // 计算新的负荷曲线数据
        const newChartData = originalLoadData.map((value) => value / kcValue);
        setChartData(newChartData);
      } else {
        // 如果输入无效或清空，恢复原始数据
        setChartData(originalLoadData);
      }
    }
  };

  // 修改表格数据结构
  const emptyTableData = [
    {
      key: '1',
      coalRate: '1.0',
      coalAvgPrice: '-',
      coalPeakPrice: '-',
    },
  ];

  const emptyGasTableData = [
    {
      key: '1',
      gasRate: '1.0',
      gasAvgPrice: '-',
      gasPeakPrice: '-',
    },
  ];

  // 更新状态管理
  const [coalTableData, setCoalTableData] = useState(emptyTableData);
  const [gasTableData, setGasTableData] = useState(emptyGasTableData);

  // 定义煤价表格列
  const coalColumns = [
    {
      title: '煤价倍率',
      dataIndex: 'coalRate',
      key: 'coalRate',
      align: 'center' as const,
    },
    {
      title: '系统平均电价（元/kWh）',
      dataIndex: 'coalAvgPrice',
      key: 'coalAvgPrice',
      align: 'center' as const,
    },
    {
      title: '系统电价峰值（元/kWh）',
      dataIndex: 'coalPeakPrice',
      key: 'coalPeakPrice',
      align: 'center' as const,
    },
  ];

  // 定义气价表格列
  const gasColumns = [
    {
      title: '气价倍率',
      dataIndex: 'gasRate',
      key: 'gasRate',
      align: 'center' as const,
    },
    {
      title: '系统平均电价（元/kWh）',
      dataIndex: 'gasAvgPrice',
      key: 'gasAvgPrice',
      align: 'center' as const,
    },
    {
      title: '系统电价峰值（元/kWh）',
      dataIndex: 'gasPeakPrice',
      key: 'gasPeakPrice',
      align: 'center' as const,
    },
  ];

  const emptyChartData = [
    Array(24).fill(0),
    Array(24).fill(0),
    Array(24).fill(0),
    Array(24).fill(0),
  ];

  // 状态管理实际数据

  const [priceChartData, setPriceChartData] = useState(emptyChartData);
  const [gasPrice, setGasPrice] = useState(emptyChartData);

  const handleRun = async () => {
    try {
      // 验证表单
      await form.validateFields();

      // 开始加载
      setIsLoading(true);

      // 调用算法
      const kcValue = form.getFieldValue('kc');
      await callMainFunctionUsingGET({ kc: kcValue });

      // 获取结果
      const result = await getSystemPriceUsingGET();

      if (result.code === 1000) {
        const { avgMaxCoal, avgMaxGas, systemPriceCoal, systemPriceGas } =
          result.data;

        // 更新煤价表格数据
        const newCoalTableData = avgMaxCoal.map((item) => ({
          key: item.id.toString(),
          coalRate: item.coalPriceRatio.replace('倍煤价', ''),
          coalAvgPrice: item.avgPrice.toString(),
          coalPeakPrice: item.maxPrice.toString(),
        }));

        // 更新气价表格数据
        const newGasTableData = avgMaxGas.map((item) => ({
          key: item.id.toString(),
          gasRate: item.gasPriceRatio.replace('倍气价', ''),
          gasAvgPrice: item.avgPrice.toString(),
          gasPeakPrice: item.maxPrice.toString(),
        }));

        // 更新煤价曲线数据
        const newPriceChartData = systemPriceCoal.map(
          (item) => item.systemPrice,
        );

        // 更新气价曲线数据
        const newGasPrice = systemPriceGas.map((item) => item.systemPrice);

        // 更新所有状态
        setCoalTableData(newCoalTableData);
        setGasTableData(newGasTableData);
        setPriceChartData(newPriceChartData);
        setGasPrice(newGasPrice);
      } else {
        message.error('获取数据失败');
      }
    } catch (error) {
      message.error('请先填写参数后再运行');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.left}>
        <CustomPane title="运行数据">
          <Form
            form={form}
            className={styles.leftContent}
            onValuesChange={handleValuesChange}
          >
            <div className={styles.formRow}>
              <div className={styles.input}>
                <div className={styles.inputTitle}>Kc</div>
                <div className={styles.inputValue}>
                  <Form.Item
                    name="kc"
                    rules={[
                      { required: true, message: '请输入Kc值' },
                      {
                        type: 'number',
                        min: 1.4,
                        max: 1.6,
                        message: 'Kc值必须在1.4-1.6之间',
                        transform: (value) => Number(value),
                      },
                    ]}
                  >
                    <CustomInput />
                  </Form.Item>
                </div>
              </div>
            </div>
          </Form>
          <div className={styles.chart}>
            <CurveChart
              data={chartData}
              yAxisName="负荷出力"
              legendName="负荷出力曲线"
              color="#6affeb"
              height={px(650)}
            />
          </div>
        </CustomPane>
      </div>
      <div className={styles.center}>
        {isLoading && (
          <div className={styles.spinContainer}>
            <Spin size="large" />
          </div>
        )}
        <Indicator title="运行" onClick={handleRun} />
      </div>
      <div className={styles.right}>
        <CustomPane title="出清结果">
          <div className={styles.tablesContainer}>
            <CustomTable
              columns={coalColumns}
              dataSource={coalTableData}
              pagination={false}
              size="small"
            />
            <CustomTable
              columns={gasColumns}
              dataSource={gasTableData}
              pagination={false}
              size="small"
            />
          </div>
          <ResultCharts priceChartData={priceChartData} gasPrice={gasPrice} />
        </CustomPane>
      </div>
    </div>
  );
}
