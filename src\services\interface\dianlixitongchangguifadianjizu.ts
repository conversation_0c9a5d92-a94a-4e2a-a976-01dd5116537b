// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/EleGenset/delete */
export async function deleteUsingDelete2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE2Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/EleGenset/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/EleGenset/page */
export async function pageUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<API.PageEleGenset>('/masc/EleGenset/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/EleGenset/save */
export async function saveUsingPost2(
  body: API.EleGenset,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/EleGenset/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/EleGenset/update */
export async function updateUsingPut2(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT2Params,
  body: API.EleGenset,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/EleGenset/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/EleGenset/upload */
export async function uploadUsingPost1(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/EleGenset/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
