import { useState } from 'react';
import styles from './index.sass';
const Menu = ({ menues, history }) => {
  const [currentRoute, setCurrentRoute] = useState(history.location.pathname);
  return (
    <div className={styles.navigation}>
      {menues.map((item, index) => (
        <div
          key={index}
          className={`${styles.navigationItem} ${
            currentRoute === item.path ? styles.activeItem : ''
          }`}
          onClick={() => {
            setCurrentRoute(item.path);
            history.push(item.path);
          }}
        >
          {item.name}
        </div>
      ))}
    </div>
  );
};

export default Menu;
