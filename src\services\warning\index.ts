import { request } from '@umijs/max';

// 气象数据接口定义
export interface MeteorologyItem {
  name: string;
  value: number;
  unit: string;
  id: number;
}

export interface MeteorologyTemperature {
  hours: number[];
  values: number[];
}

export interface MeteorologyData {
  meteorologicalTemperature: MeteorologyTemperature;
  meteorologicalData: MeteorologyItem[];
}

// 气象数据接口
export interface MeteorologicalTemperature {
  hours: number[];
  values: number[];
}

export interface MeteorologicalDataItem {
  name: string;
  value: number;
  unit: string;
  id: number;
}

export interface MeteorologicalResponse {
  meteorologicalTemperature: MeteorologicalTemperature;
  meteorologicalData: MeteorologicalDataItem[];
}

// 获取气象数据
export async function getWarningMeteorological(): Promise<MeteorologyData> {
  return request('/masc/WarningMeteorological/query', {
    method: 'GET',
  });
}

export async function queryMeteorological(): Promise<MeteorologicalResponse> {
  return request('/masc/WarningMeteorological/query');
}

// 气象数据接口定义
export interface MeteorologyItem {
  name: string;
  value: number;
  unit: string;
  id: number;
}

export interface MeteorologyTemperature {
  hours: number[];
  values: number[];
}

export interface MeteorologyData {
  meteorologicalTemperature: MeteorologyTemperature;
  meteorologicalData: MeteorologyItem[];
}

// 覆冰数据接口定义
export interface IceCoverItem {
  name: string;
  value: number;
  unit: string;
  id: number;
}

export interface IceCoverData {
  iceCoverData: IceCoverItem[];
  meteorologicalTemperature: MeteorologyTemperature;
}

// 负荷数据接口定义
export interface LoadData {
  hours: number[];
  values: number[];
}

// 受灾区域数据接口定义
export interface DisasterAreaItem {
  name: string;
  value: string;
  unit: string | null;
  id: number;
}

// 风险预警数据接口定义
export interface KeyIndicatorItem {
  name: string;
  value: number;
  id: number;
}

export interface WeightDistributionItem {
  name: string;
  value: string;
  id: number;
}

export interface RiskItem {
  name: string;
  value: number;
  id: number;
}

export interface RiskEarlyWarningData {
  keyIndicator: KeyIndicatorItem[];
  weightDistribution: WeightDistributionItem[];
  risk: RiskItem[];
}

// 下载气象数据Excel文件
export async function downloadWarningMeteorological(): Promise<Blob> {
  return request('/masc/WarningMeteorological/download', {
    method: 'GET',
    responseType: 'blob',
  });
}

// 获取覆冰数据
export async function getWarningIceCover(): Promise<IceCoverData> {
  return request('/masc/WarningIceCover/query', {
    method: 'GET',
  });
}

// 下载覆冰数据Excel文件
export async function downloadWarningIceCover(): Promise<Blob> {
  return request('/masc/WarningIceCover/download', {
    method: 'GET',
    responseType: 'blob',
  });
}

// 获取负荷数据
export async function getWarningLoad(): Promise<LoadData> {
  return request('/masc/WarningLoad/query', {
    method: 'GET',
  });
}

// 下载负荷数据Excel文件
export async function downloadWarningLoad(): Promise<Blob> {
  return request('/masc/WarningLoad/download', {
    method: 'GET',
    responseType: 'blob',
  });
}

// 上传负荷数据Excel文件
export async function uploadWarningLoad(file: File): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);

  return request('/masc/WarningLoad/upload', {
    method: 'POST',
    data: formData,
    requestType: 'form',
  });
}

// 获取受灾区域数据
export async function getWarningDisasterArea(): Promise<DisasterAreaItem[]> {
  return request('/masc/WarningDisasterArea/query', {
    method: 'GET',
  });
}

// 下载受灾区域数据Excel文件
export async function downloadWarningDisasterArea(): Promise<Blob> {
  return request('/masc/WarningDisasterArea/download', {
    method: 'GET',
    responseType: 'blob',
  });
}

// 上传受灾区域数据Excel文件
export async function uploadWarningDisasterArea(file: File): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);

  return request('/masc/WarningDisasterArea/upload', {
    method: 'POST',
    data: formData,
    requestType: 'form',
  });
}

// 获取风险预警数据
export async function getWarningRiskEarlyWarning(): Promise<RiskEarlyWarningData> {
  return request('/masc/WarningRiskEarlyWarning/query', {
    method: 'GET',
  });
}

// 下载关键指标Excel文件
export async function downloadWarningKeyIndicator(): Promise<Blob> {
  return request('/masc/WarningRiskEarlyWarning/downloadKeyIndicator', {
    method: 'GET',
    responseType: 'blob',
  });
}

// 下载综合风险评估报告Excel文件
export async function downloadWarningComprehensiveRiskAssessment(): Promise<Blob> {
  return request(
    '/masc/WarningRiskEarlyWarning/downloadComprehensiveRiskAssessmentReport',
    {
      method: 'GET',
      responseType: 'blob',
    },
  );
}
