{"private": true, "author": "ttc <<EMAIL>>", "scripts": {"build": "max build", "dev": "max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/icons": "^5.0.1", "@ant-design/pro-components": "^2.4.4", "@chakra-ui/react": "^2.8.2", "@umijs/max": "^4.3.12", "antd": "^5.4.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "xlsx": "^0.18.5", "zustand": "^4.5.5"}, "devDependencies": {"@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "husky": "^9", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}}