// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/LoadAndNewEnergy/delete */
export async function deleteUsingDELETE14(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE14Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/LoadAndNewEnergy/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询全部 GET /masc/LoadAndNewEnergy/findAll */
export async function findAllUsingGET1(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/masc/LoadAndNewEnergy/findAll', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/LoadAndNewEnergy/save */
export async function saveUsingPOST14(
  body: API.LoadAndNewEnergy,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/LoadAndNewEnergy/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/LoadAndNewEnergy/update */
export async function updateUsingPUT14(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT14Params,
  body: API.LoadAndNewEnergy,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/LoadAndNewEnergy/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/LoadAndNewEnergy/upload */
export async function uploadUsingPOST14(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/LoadAndNewEnergy/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
