// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 查询所有机组编号 GET /masc/GasGenQuoteCapacity/findAllGensetNumber */
export async function findAllGensetNumberUsingGET1(options?: {
  [key: string]: any;
}) {
  return request<number[]>('/masc/GasGenQuoteCapacity/findAllGensetNumber', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据机组编号查询,quote报价集合,capacity报量集合 GET /masc/GasGenQuoteCapacity/findByGensetNumber */
export async function findByGensetNumberUsingGET1(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.findByGensetNumberUsingGET1Params,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>(
    '/masc/GasGenQuoteCapacity/findByGensetNumber',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 表格上传 POST /masc/GasGenQuoteCapacity/upload */
export async function uploadUsingPOST9(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasGenQuoteCapacity/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
