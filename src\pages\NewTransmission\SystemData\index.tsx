import { useModel } from '@umijs/max';
import React from 'react';
import GasNode from './GasNode';
import GasPipe from './GasPipe';
import PowerLine from './PowerLine';
import PowerNode from './PowerNode';
import styles from './index.sass';
const SystemPage: React.FC = () => {
  //根据key的值选择展示
  const { key } = useModel('menu');
  return (
    <div className={styles.box}>
      {key === 1 && <GasNode />}
      {key === 2 && <GasPipe />}
      {key === 3 && <PowerNode />}
      {key === 4 && <PowerLine />}
    </div>
  );
};

export default SystemPage;
