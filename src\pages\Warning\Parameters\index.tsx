import React, { useEffect, useRef, useState } from 'react';
import * as echarts from 'echarts';
import styles from './index.module.sass';
import CustomPane from '@/components/CustomPane';
import ReactECharts from 'echarts-for-react';
const ElectrolyticAluminumCalculator: React.FC = () => {
  const currentChartRef = useRef<HTMLDivElement>(null);
  const voltageChartRef = useRef<HTMLDivElement>(null);
  const powerChartRef = useRef<HTMLDivElement>(null);
  
  const [isCalculating, setIsCalculating] = useState(false);
  const [buttonText, setButtonText] = useState('计算极限可调有功');
  const [isPulse, setIsPulse] = useState(false);

  const [formData, setFormData] = useState({
    activePower: 120,
    refCurrent: 320,
    dcVoltage: 384,
    currentReduction: 10.5,
    maxVoltageDrop: 54
  });
   const hours = ['0:15', '0:30', '0:45', '1:00', '1:15', '1:30', '1:45', '2:00', '2:15', '2:30', '2:45', '3:00',
                '3:15', '3:30', '3:45', '4:00', '4:15', '4:30', '4:45', '5:00', '5:15', '5:30', '5:45', '6:00',
                '6:15', '6:30', '6:45', '7:00', '7:15', '7:30', '7:45', '8:00', '8:15', '8:30', '8:45', '9:00',
                '9:15', '9:30', '9:45', '10:00', '10:15', '10:30', '10:45', '11:00', '11:15', '11:30', '11:45', '12:00',
                '12:15', '12:30', '12:45', '13:00', '13:15', '13:30', '13:45', '14:00', '14:15', '14:30', '14:45', '15:00',
                '15:15', '15:30', '15:45', '16:00', '16:15', '16:30', '16:45', '17:00', '17:15', '17:30', '17:45', '18:00',
                '18:15', '18:30', '18:45', '19:00', '19:15', '19:30', '19:45', '20:00', '20:15', '20:30', '20:45'];

  const forecastOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(10, 20, 40, 0.8)',
      borderColor: 'rgba(0, 174, 255, 0.5)',
      textStyle: {
        color: '#fff'
      },
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: 'rgba(106, 255, 235, 0.5)'
        }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      containLabel: true,
      top: '20%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        interval: 5
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.05)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: ' ',
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.6)',
        padding: [0, 0, 0, -40]
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.05)'
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '预测负荷',
        type: 'line',
        data: [7879.5, 7838.3, 7802.4, 7805.7, 7717.9, 7734.9, 7681.2, 7725.6, 7787.9, 7744, 7578.2, 7623.2,
               7700.3, 7726.5, 7700.4, 7808.5, 7876.2, 7927.5, 8171, 8269.7, 8349.9, 8491.6, 8727.5, 8773.1,
               9221.1, 9278.1, 9607.8, 9704.4, 9883.3, 9953.3, 10090, 10141, 10151, 10126, 10004, 9995.4,
               10028, 9767.8, 9217.2, 9266.5, 9391.9, 9490.1, 10189, 10103, 9953.8, 9922.3, 10027, 10080,
               9826.3, 9864.1, 9698.8, 9612.7, 9713.3, 9698.6, 9659.4, 9707.4, 9709.1, 9767.2, 9746.7, 9739.6,
               9475.1, 9383.6, 8990.2, 9162.2, 9269.5, 9345.7, 9534.6, 9648.2, 9641.5, 9681.3, 9704.1, 9712.2,
               9718.5, 9659, 9572, 9548.5, 9534.3, 9565.9, 9668.5, 9580.1, 9375.3, 9300.5, 8972.1],
        smooth: true,
        lineStyle: {
          width: 4,
          color: '#6affeb'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#6affeb'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(106, 255, 235, 0.4)' },
              { offset: 1, color: 'rgba(106, 255, 235, 0.05)' }
            ]
          }
        }
      }
    ]
  };

  const actualOption = {
    ...forecastOption,
    series: [
      {
        name: '实际负荷',
        type: 'line',
        data: [8237, 8186, 8099, 7913, 7885, 7925, 7890, 7792, 7814, 7791, 7687, 7700,
               7649, 7688, 7744, 7639, 7563, 7605, 7634, 7636, 7612, 7762, 7774, 7838,
               7988, 8128, 8210, 8420, 8491, 8584, 9081, 9189, 9365, 9544, 9796, 9993,
               10001, 10188, 10173, 10207, 10157, 10179, 10190, 9727, 9624, 9564, 9648, 9775,
               10173, 10117, 10099, 10054, 10142, 10152, 9970, 10032, 9927, 9857, 9908, 9857,
               9859, 9907, 9872, 9960, 9877, 9884, 9622, 9451, 9292, 9402, 9468, 9593,
               9591, 9668, 9717, 9769, 9742, 9789, 9831, 9726, 9743, 9703, 9667],
        smooth: true,
        lineStyle: {
          width: 4,
          color: '#00aeff'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00aeff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 174, 255, 0.4)' },
              { offset: 1, color: 'rgba(0, 174, 255, 0.05)' }
            ]
          }
        }
      }
    ]
  };

  useEffect(() => {
    initializeCharts();
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const initializeCharts = () => {
    if (!currentChartRef.current || !voltageChartRef.current || !powerChartRef.current) return;

    const currentChart = echarts.init(currentChartRef.current);
    const voltageChart = echarts.init(voltageChartRef.current);
    const powerChart = echarts.init(powerChartRef.current);

    const timePoints = ['调节前', '调节后'];

    const commonChartConfig = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(10, 20, 40, 0.8)',
        borderColor: 'rgba(0, 174, 255, 0.5)',
        textStyle: { color: '#fff' },
        axisPointer: {
          type: 'line',
          lineStyle: { color: 'rgba(106, 255, 235, 0.5)' }
        }
      },
      grid: {
        left: '15%',
        right: '10%',
        bottom: '15%',
        top: '20%'
      },
      xAxis: {
        type: 'category',
        data: timePoints,
        axisLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.3)' } },
        axisLabel: { color: 'rgba(255, 255, 255, 0.6)' }
      }
    };

    const createYAxis = (name: string, min: number, max: number) => ({
      type: 'value',
      name,
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.6)',
        padding: [0, 0, 0, 10]
      },
      min,
      max,
      splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.05)' } },
      axisLine: {
        show: true,
        lineStyle: { color: 'rgba(255, 255, 255, 0.3)' }
      },
      axisLabel: { color: 'rgba(255, 255, 255, 0.6)' }
    });

    const createSeries = (name: string, data: number[], color: string, unit: string) => ({
      name,
      type: 'line',
      data,
      smooth: true,
      lineStyle: { width: 3, color },
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: { color },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: `${color}4D` },
          { offset: 1, color: `${color}0D` }
        ])
      },
      label: {
        show: true,
        position: 'top',
        color: '#6affeb',
        fontSize: 12,
        formatter: `{c} ${unit}`
      }
    });

    // Current chart
    currentChart.setOption({
      ...commonChartConfig,
      tooltip: {
        ...commonChartConfig.tooltip,
        formatter: '{b0}<br/>{a0}: {c0} kA'
      },
      yAxis: createYAxis('电流 (kA)', 280, 330),
      series: [createSeries('电解电流', [320, 293], '#00aeff', 'kA')]
    });

    // Voltage chart
    voltageChart.setOption({
      ...commonChartConfig,
      tooltip: {
        ...commonChartConfig.tooltip,
        formatter: '{b0}<br/>{a0}: {c0} V'
      },
      yAxis: createYAxis('电压 (V)', 320, 390),
      series: [createSeries('直流电压', [384, 330], '#6affeb', 'V')]
    });

    // Power chart
    powerChart.setOption({
      ...commonChartConfig,
      tooltip: {
        ...commonChartConfig.tooltip,
        formatter: '{b0}<br/>{a0}: {c0} MW'
      },
      yAxis: createYAxis('功率 (MW)', 0, 130),
      series: [createSeries('负荷功率', [120, 26], '#00f7ff', 'MW')]
    });
  };

  const handleResize = () => {
    if (currentChartRef.current && voltageChartRef.current && powerChartRef.current) {
      echarts.getInstanceByDom(currentChartRef.current)?.resize();
      echarts.getInstanceByDom(voltageChartRef.current)?.resize();
      echarts.getInstanceByDom(powerChartRef.current)?.resize();
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCalculate = () => {
    setIsCalculating(true);
    setButtonText('计算中...');
    
    setTimeout(() => {
      setButtonText('计算完成');
      setIsPulse(true);
      
      setTimeout(() => {
        setIsPulse(false);
        setButtonText('重新计算');
        setIsCalculating(false);
      }, 2000);
    }, 1500);
  };

  return (
    <div className={styles.electrolyticAluminumCalculator}>
      <div className={styles.gridOverlay}></div>
      <div className={`${styles.glow} ${styles.glow1}`}></div>
      <div className={`${styles.glow} ${styles.glow2}`}></div>

      <div className={styles.container}>
   

        {/* 左侧输入面板 */}
        <div className={styles.inputPanel}>
          {/* <div className={styles.panelTitle}>
            <i className="fas fa-sliders-h"></i>
            设备参数设置
          </div> */} 
          <CustomPane title="设备参数设置">
            <div className={styles.inputSection}>
              <div className={styles.inputGrid}>
              <div className={styles.inputGroup}>
                <label>调节前有功功率 (MW)</label>
                <div className={styles.inputWrapper}>
                  <input
                    type="number"
                    value={formData.activePower}
                    onChange={(e) => handleInputChange('activePower', Number(e.target.value))}
                  />
                </div>
              </div>

              <div className={styles.inputGroup}>
                <label>调节前参考电流 (kA)</label>
                <div className={styles.inputWrapper}>
                  <input
                    type="number"
                    value={formData.refCurrent}
                    onChange={(e) => handleInputChange('refCurrent', Number(e.target.value))}
                  />
                  <div className={styles.units}>kA</div>
                </div>
              </div>
              
              <div className={styles.inputGroup}>
                <label>调节前直流输出电压 (V)</label>
                <div className={styles.inputWrapper}>
                  <input
                    type="number"
                    value={formData.dcVoltage}
                    onChange={(e) => handleInputChange('dcVoltage', Number(e.target.value))}
                  />
                  <div className={styles.units}>V</div>
                </div>
              </div>

              <div className={styles.inputGroup}>
                <label>参考电流降低百分比</label>
                <div className={styles.inputWrapper}>
                  <input
                    type="number"
                    value={formData.currentReduction}
                    onChange={(e) => handleInputChange('currentReduction', Number(e.target.value))}
                  />
                  <div className={styles.units}>%</div>
                </div>
              </div>

              <div className={styles.inputGroup}>
                <label>饱和电抗器最大调压深度</label>
                <div className={styles.inputWrapper}>
                  <input
                    type="number"
                    value={formData.maxVoltageDrop}
                    onChange={(e) => handleInputChange('maxVoltageDrop', Number(e.target.value))}
                  />
                  <div className={styles.units}>V</div>
                </div>
              </div>
            </div>
            
            <div className={styles.btnContainer}>
              <button
                className={`${styles.calculateBtn} ${isCalculating ? styles.calculating : ''}`}
                onClick={handleCalculate}
                disabled={isCalculating}
              >
                <i className={`fas ${isCalculating ? 'fa-spinner fa-spin' : buttonText === '计算完成' ? 'fa-check' : 'fa-bolt'}`}></i>
                {buttonText}
              </button>
              </div>
            </div>
          </CustomPane>

       
        </div>

        {/* 中间图表面板 */}
        <div className={styles.chartPanel}>
          <div className={styles.chartContainer}>
            <CustomPane title="电解电流变化情况 (kA)">
                 <ReactECharts 
                      option={forecastOption} 
                      style={{ height: '100%', width: '100%' }}
                      className="chart"
                        />
            </CustomPane>
          </div>

          <div className={styles.chartContainer}>
            <CustomPane title="直流输出电压变化情况 (V)">
        <ReactECharts 
                          option={forecastOption} 
                          style={{ height: '100%', width: '100%' }}
                          className="chart"
                        />
            </CustomPane>
          </div>

          <div className={styles.chartContainer}>
            <CustomPane title="负荷消耗功率变化情况 (MW)">
          <ReactECharts 
                          option={forecastOption} 
                          style={{ height: '100%', width: '100%' }}
                          className="chart"
                        />
            </CustomPane>
          </div>
        </div>

        {/* 右侧结果面板 */}
        <CustomPane title={'分析结果'}>
        <div className={styles.resultPanel}>
          <div className={styles.resultItem}>
            <div className={styles.resultName}>直流输出电压</div>
            <div className={styles.resultValue}>330V</div>
            <div className={styles.resultChange}>
              <i className="fas fa-arrow-down"></i>
              降低 54V
            </div>
          </div>

          <div className={styles.resultItem}>
            <div className={styles.resultName}>系列电流</div>
            <div className={styles.resultValue}>293kA</div>
            <div className={styles.resultChange}>
              <i className="fas fa-arrow-down"></i>
              降低 27kA
            </div>
          </div>

          <div className={styles.resultItem}>
            <div className={styles.resultName}>负荷消耗功率</div>
            <div className={styles.resultValue}>26MW</div>
            <div className={styles.resultChange}>
              <i className="fas fa-exchange-alt"></i>
              120MW → 26MW
            </div>
          </div>

          <div className={`${styles.resultItem} ${styles.highlight} ${isPulse ? styles.pulse : ''}`}>
            <div className={styles.resultName}>最大有功支撑</div>
            <div className={styles.resultValue}>26MW</div>
            <div className={styles.resultChange}>
              <i className="fas fa-bolt"></i>
              极限可调有功
            </div>
          </div>
        </div>
        </CustomPane>
      </div>
    </div>
  );
};

export default ElectrolyticAluminumCalculator;