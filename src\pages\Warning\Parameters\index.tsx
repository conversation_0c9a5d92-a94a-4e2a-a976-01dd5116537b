import React, { useState } from 'react';
import styles from './index.module.sass';
import CustomPane from '@/components/CustomPane';
import ReactECharts from 'echarts-for-react';

const ClusterLoadResponseCalculator: React.FC = () => {
  const [isCalculating, setIsCalculating] = useState(false);
  const [buttonText, setButtonText] = useState('导入数据');
  const [isPulse, setIsPulse] = useState(false);

  // 电动汽车集群基础参数
  const [clusterParams, setClusterParams] = useState({
    evCount: 500,           // 电动汽车数量 辆
    avgBatteryCapacity: 60, // 单车电池容量 kWh
    chargingEfficiency: 0.9, // 充电桩充电功率 kW
    avgDailyMileage: 50     // 每公里耗电量 kWh/km
  });

  // 用户行为及初始状态参数
  const [userBehaviorParams, setUserBehaviorParams] = useState({
    dailyTripDistribution: 0.8,    // 日行驶里程分布参数 均值_标准差
    returnTimeDistribution: 18.5,   // 返回时刻（开始充电时刻）均值_标准差
    departureTimeDistribution: 8.0, // 始发时刻（结束充电时刻）均值_标准差
    userChargingDemand: 0.85       // 用户充电期望电量
  });

  // 约束参数
  const [constraintParams, setConstraintParams] = useState({
    infrastructureConstraint: 1000, // 基础负荷曲线
    timeConstraint: 24,            // 分时电价表
    batteryCapacityConstraint: 80, // 电池可用容量约束
    transformerConstraint: 2000    // 变压器容量约束
  });

  // 计算结果
  const [results, setResults] = useState({
    evCount: 200,           // 电动汽车数量/辆
    peakPowerReduction: 5016.0,  // 负荷峰值/kW
    valleyPowerIncrease: 4169.6, // 负荷峰谷差/kW
    peakValleyRatio: 83.13       // 负荷峰谷比/%
  });

  const handleInputChange = (section: string, field: string, value: number) => {
    if (section === 'cluster') {
      setClusterParams(prev => ({ ...prev, [field]: value }));
    } else if (section === 'behavior') {
      setUserBehaviorParams(prev => ({ ...prev, [field]: value }));
    } else if (section === 'constraint') {
      setConstraintParams(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleImportData = async () => {
    setIsCalculating(true);
    setButtonText('导入中...');
    setIsPulse(true);

    // 模拟数据导入过程
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 模拟计算结果更新
    setResults({
      evCount: clusterParams.evCount,
      peakPowerReduction: 5016.0 + Math.random() * 1000,
      valleyPowerIncrease: 4169.6 + Math.random() * 500,
      peakValleyRatio: 83.13 + Math.random() * 10
    });

    setIsCalculating(false);
    setButtonText('重新导入');
    setIsPulse(false);
  };

  // 负荷曲线图表配置
  const loadCurveOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00aeff',
      borderWidth: 1,
      textStyle: { color: '#fff' },
      formatter: function(params: any) {
        let result = `时间: ${params[0].name}时<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value} kW<br/>`;
        });
        return result;
      }
    },
    legend: {
      data: ['基础负荷', '有序充电负荷', '无序充电负荷'],
      textStyle: { color: '#6affeb' },
      top: '5%'
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0', '2', '4', '6', '8', '10', '12', '14', '16', '18', '20', '22', '24'],
      axisLine: { lineStyle: { color: '#00aeff' } },
      axisLabel: { color: '#6affeb', fontSize: 12 },
      splitLine: { show: false },
      name: '时间(h)',
      nameTextStyle: { color: '#6affeb' }
    },
    yAxis: {
      type: 'value',
      name: '功率(kW)',
      nameTextStyle: { color: '#6affeb' },
      axisLine: { lineStyle: { color: '#00aeff' } },
      axisLabel: { color: '#6affeb', fontSize: 12 },
      splitLine: { 
        lineStyle: { 
          color: 'rgba(0, 174, 255, 0.2)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '基础负荷',
        type: 'line',
        data: [1000, 950, 900, 850, 900, 1200, 1800, 2200, 2000, 1800, 1600, 1400, 1200, 1500, 1800, 2200, 2500, 2800, 2600, 2200, 1800, 1500, 1200, 1100],
        smooth: true,
        lineStyle: { width: 2, color: '#6affeb' },
        symbol: 'none',
        itemStyle: { color: '#6affeb' }
      },
      {
        name: '有序充电负荷',
        type: 'line',
        data: [1000, 950, 900, 850, 900, 1200, 1500, 1800, 2000, 2200, 2800, 3200, 3500, 3800, 4200, 4500, 4200, 3800, 3200, 2800, 2200, 1800, 1400, 1200],
        smooth: true,
        lineStyle: { width: 2, color: '#00aeff' },
        symbol: 'none',
        itemStyle: { color: '#00aeff' }
      },
      {
        name: '无序充电负荷',
        type: 'line',
        data: [1200, 1100, 1000, 950, 1000, 1300, 1600, 2000, 2400, 2800, 3500, 4200, 4800, 5200, 5000, 4600, 4200, 3800, 3200, 2600, 2200, 1800, 1500, 1300],
        smooth: true,
        lineStyle: { width: 2, color: '#ff6b6b', type: 'dashed' },
        symbol: 'none',
        itemStyle: { color: '#ff6b6b' }
      }
    ]
  };

  return (
    <div className={styles.electrolyticAluminumCalculator}>
      <div className={styles.gridOverlay}></div>
      
      <div className={styles.container}>
        {/* 头部标题 */}
        

        {/* 左侧输入面板 */}
        <div className={styles.inputPanel}>
          <CustomPane title="电动汽车集群基础参数">
            <div className={styles.inputSection}>
              <div className={styles.inputGrid}>
                <div className={styles.inputGroup}>
                  <label>电动汽车数量</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={clusterParams.evCount}
                      onChange={(e) => handleInputChange('cluster', 'evCount', Number(e.target.value))}
                    />
                    <span className={styles.units}>辆</span>
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>单车电池容量</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={clusterParams.avgBatteryCapacity}
                      onChange={(e) => handleInputChange('cluster', 'avgBatteryCapacity', Number(e.target.value))}
                    />
                    <span className={styles.units}>kWh</span>
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>充电桩充电功率</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      step="0.1"
                      value={clusterParams.chargingEfficiency}
                      onChange={(e) => handleInputChange('cluster', 'chargingEfficiency', Number(e.target.value))}
                    />
                    <span className={styles.units}>kW</span>
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>每公里耗电量</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={clusterParams.avgDailyMileage}
                      onChange={(e) => handleInputChange('cluster', 'avgDailyMileage', Number(e.target.value))}
                    />
                    <span className={styles.units}>kWh/km</span>
                  </div>
                </div>
              </div>
            </div>
          </CustomPane>

          <CustomPane title="用户行为及初始状态参数">
            <div className={styles.inputSection}>
              <div className={styles.inputGrid}>
                <div className={styles.inputGroup}>
                  <label>日行驶里程分布参数 均值_标准差</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      step="0.1"
                      value={userBehaviorParams.dailyTripDistribution}
                      onChange={(e) => handleInputChange('behavior', 'dailyTripDistribution', Number(e.target.value))}
                    />
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>返回时刻（开始充电时刻）均值_标准差</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      step="0.1"
                      value={userBehaviorParams.returnTimeDistribution}
                      onChange={(e) => handleInputChange('behavior', 'returnTimeDistribution', Number(e.target.value))}
                    />
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>始发时刻（结束充电时刻）均值_标准差</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      step="0.1"
                      value={userBehaviorParams.departureTimeDistribution}
                      onChange={(e) => handleInputChange('behavior', 'departureTimeDistribution', Number(e.target.value))}
                    />
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>用户充电期望电量</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      step="0.01"
                      value={userBehaviorParams.userChargingDemand}
                      onChange={(e) => handleInputChange('behavior', 'userChargingDemand', Number(e.target.value))}
                    />
                  </div>
                </div>
              </div>
            </div>
          </CustomPane>

          <CustomPane title="约束参数">
            <div className={styles.inputSection}>
              <div className={styles.inputGrid}>
                <div className={styles.inputGroup}>
                  <label>基础负荷曲线</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={constraintParams.infrastructureConstraint}
                      onChange={(e) => handleInputChange('constraint', 'infrastructureConstraint', Number(e.target.value))}
                    />
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>分时电价表</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={constraintParams.timeConstraint}
                      onChange={(e) => handleInputChange('constraint', 'timeConstraint', Number(e.target.value))}
                    />
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>电池可用容量约束</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={constraintParams.batteryCapacityConstraint}
                      onChange={(e) => handleInputChange('constraint', 'batteryCapacityConstraint', Number(e.target.value))}
                    />
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>变压器容量约束</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={constraintParams.transformerConstraint}
                      onChange={(e) => handleInputChange('constraint', 'transformerConstraint', Number(e.target.value))}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.btnContainer}  >
              <button
                className={`${styles.calculateBtn} ${isPulse ? styles.pulse : ''}`}
                onClick={handleImportData}
                disabled={isCalculating}
              >
                {buttonText}
              </button>
            </div>
          </CustomPane>
        </div>

        {/* 中间图表面板 */}
        <div className={styles.chartPanel}>
          <CustomPane title="负荷曲线">
            <div className={styles.chartContainer}>
              <ReactECharts
                option={loadCurveOption}
                style={{ height: '100%', width: '100%' }}
                className="chart"
              />
            </div>
          </CustomPane>
        </div>

        {/* 底部结果面板 */}
        <CustomPane title="输出结果">
          <div className={styles.resultPanel}>
            <div className={styles.resultItem}>
              <div className={styles.resultName}>电动汽车数量/辆</div>
              <div className={styles.resultValue}>{results.evCount}</div>
            </div>

            <div className={styles.resultItem}>
              <div className={styles.resultName}>负荷峰值/kW</div>
              <div className={styles.resultValue}>{results.peakPowerReduction.toFixed(1)}</div>
            </div>

            <div className={styles.resultItem}>
              <div className={styles.resultName}>负荷峰谷差/kW</div>
              <div className={styles.resultValue}>{results.valleyPowerIncrease.toFixed(1)}</div>
            </div>

            <div className={styles.resultItem}>
              <div className={styles.resultName}>负荷峰谷比/%</div>
              <div className={styles.resultValue}>{results.peakValleyRatio.toFixed(2)}</div>
            </div>
          </div>
        </CustomPane>
      </div>
    </div>
  );
};

export default ClusterLoadResponseCalculator;
