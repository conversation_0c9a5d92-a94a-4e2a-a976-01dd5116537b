import px from '@/utils/px';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';

interface MultiCurveChartProps {
  data: number[][];
  yAxisName?: string;
  legendNames?: string[];
  height?: number;
  title?: string;
}

const MultiLineChart: React.FC<MultiCurveChartProps> = ({
  data,
  title,
  yAxisName = '',
  legendNames = [],
  height = px(240),
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  const colors = ['#6affeb', '#ff7f50', '#87cefa', '#ffd700'];

  useEffect(() => {
    if (!chartRef.current) return;

    const myChart = echarts.init(chartRef.current);

    const xData = Array.from({ length: data[0].length }, (_, i) => i + 1);

    const option = {
      title: {
        text: title,
        left: 'center',
        top: '1%',
        textStyle: {
          color: 'white',
          fontSize: px(16),
        },
      },
      legend: {
        show: true,
        data: legendNames,
        right: '10%',
        top: '6%',
        textStyle: {
          fontSize: px(14),
          color: 'white',
        },
        itemStyle: {
          borderColor: 'transparent',
        },
      },
      series: data.map((lineData, index) => ({
        name: legendNames[index],
        data: lineData,
        type: 'line',
        smooth: false,
        showSymbol: false,
        symbolSize: 10,
        emphasis: { focus: 'series' },
        animationDuration: 2500,
        animationEasing: 'cubicInOut',
        lineStyle: {
          width: 2,
          color: colors[index],
        },

        itemStyle: {
          color: colors[index],
        },
      })),
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
        },
        textStyle: {
          color: '#fafafa',
        },
        borderColor: 'transparent',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        extraCssText: 'backdrop-filter: blur(6px);',
      },
      grid: {
        top: '25%',
        left: '5%',
        right: '2.5%',
        bottom: '0%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xData,
        axisLine: { show: false },
        axisTick: { show: false },
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLabel: {
          color: 'white',
          fontSize: px(14),
          formatter: '{value}时',
        },
      },
      yAxis: {
        name: yAxisName,
        type: 'value',
        nameLocation: 'end',
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLabel: {
          color: 'white',
          fontSize: px(14),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#062b45',
            width: 1,
            type: 'dashed',
          },
        },
      },
    };

    myChart.setOption(option);

    return () => {
      myChart.dispose();
    };
  }, [data, yAxisName, legendNames, title]);

  return <div ref={chartRef} style={{ width: '100%', height: px(height) }} />;
};

export default MultiLineChart;
