import * as echarts from 'echarts';
import React, { useEffect, useMemo, useRef } from 'react';

interface ResourcePieProps {
  data: Array<{
    name: string;
    value: number;
    percentage?: string;
  }>;
  colors?: string[];
}

const ResourcePie: React.FC<ResourcePieProps> = ({
  data,
  colors = ['#00ffff', '#00cfff', '#006ced', '#ffe000'],
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();

  // 计算总和和百分比
  const total = useMemo(
    () => data.reduce((sum, item) => sum + item.value, 0),
    [data],
  );

  // 缓存图表配置
  const option = useMemo(
    () => ({
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      legend: {
        type: 'plain',
        orient: 'vertical',
        right: '0%',
        top: 'middle',
        itemWidth: 25,
        itemHeight: 14,
        itemGap: 20,
        formatter: function (name: string) {
          const item = data.find((d) => d.name === name);
          if (!item) return name;
          // 使用API返回的percentage字段，如果存在的话
          const percentage = item.percentage
            ? item.percentage
            : ((item.value / total) * 100).toFixed(1) + '%';
          return `${name}  ${item.value}  （${percentage}）`;
        },
        textStyle: {
          color: '#fff',
          fontSize: 14,
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['35%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          emphasis: {
            label: {
              show: false,
              fontSize: 14,
            },
          },
          data: data.map((item, index) => ({
            ...item,
            itemStyle: {
              color: colors[index % colors.length],
            },
          })),
          animation: false, // 禁用动画以减少抖动
        },
      ],
    }),
    [data, colors, total],
  );

  useEffect(() => {
    if (!chartRef.current) return;

    // 只在第一次渲染时初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    // 更新图表配置
    chartInstance.current.setOption(option, {
      notMerge: false, // 允许合并配置而不是完全替换
    });

    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);
    const resizeObserver = new ResizeObserver(() => {
      chartInstance.current?.resize();
    });

    if (chartRef.current) {
      resizeObserver.observe(chartRef.current);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      resizeObserver.disconnect();
      chartInstance.current?.dispose();
      chartInstance.current = undefined;
    };
  }, [option]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

export default React.memo(ResourcePie);
