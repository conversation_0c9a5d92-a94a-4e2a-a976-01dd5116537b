<svg width="279.126709" height="70.008179" viewBox="0 0 279.127 70.0082" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_116_78_dd" x="0.000000" y="1.991821" width="279.126709" height="58.016357" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="8.33606" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_116_79_dd" x="27.110352" y="11.991821" width="224.016113" height="58.016357" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="8.33606" result="effect_layerBlur_1"/>
		</filter>
		<filter id="filter_116_80_dd" x="12.578125" y="6.498657" width="27.095703" height="27.002686" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.91667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.99216 0 0 0 0 0.74118 0 0 0 0 0 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="3.83333"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.99216 0 0 0 0 0.77255 0 0 0 0 0 0 0 0 1 0"/>
			<feBlend mode="normal" in2="effect_dropShadow_1" result="effect_dropShadow_2"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_2" result="shape"/>
		</filter>
		<filter id="filter_116_81_dd" x="230.354492" y="6.498657" width="27.095703" height="27.002686" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.91667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.99216 0 0 0 0 0.74118 0 0 0 0 0 0 0 0 1 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="3.83333"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.99216 0 0 0 0 0.77255 0 0 0 0 0 0 0 0 1 0"/>
			<feBlend mode="normal" in2="effect_dropShadow_1" result="effect_dropShadow_2"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_2" result="shape"/>
		</filter>
	</defs>
	<path id="蒙版" d="M20.11 0L253.91 0L253.91 41.33C253.91 41.33 195.3 37.33 136.91 37.33C78.51 37.33 20.11 41.33 20.11 41.33L20.11 0Z" fill="#D8D8D8" fill-opacity="0" fill-rule="evenodd"/>
	<path id="蒙版" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
	<mask id="mask116_76" mask-type="alpha" maskUnits="userSpaceOnUse" x="20.118408" y="0.000000" width="233.791779" height="41.333313">
		<path id="蒙版" d="M20.11 0L253.91 0L253.91 41.33C253.91 41.33 195.3 37.33 136.91 37.33C78.51 37.33 20.11 41.33 20.11 41.33L20.11 0Z" fill="#C4C4C4" fill-opacity="1.000000" fill-rule="evenodd"/>
	</mask>
	<mask id="mask116_77" mask-type="alpha" maskUnits="userSpaceOnUse" x="18.022949" y="0.000000" width="233.301575" height="40.999985">
		<path id="蒙版" d="M18.02 0L251.32 0L251.32 41C251.32 41 192.84 37.03 134.57 37.03C76.29 37.03 18.02 41 18.02 41L18.02 0Z" fill="#C4C4C4" fill-opacity="1.000000" fill-rule="evenodd"/>
	</mask>
	<g mask="url(#mask116_77)">
		<g filter="url(#filter_116_78_dd)">
			<rect id="炫光" x="25.008057" y="27.000000" width="229.110535" height="7.999997" fill="#3BD2E9" fill-opacity="1.000000"/>
			<rect id="炫光" x="25.583008" y="27.575012" width="227.960541" height="6.849997" stroke="#979797" stroke-opacity="0" stroke-width="1.150000"/>
		</g>
	</g>
	<g mask="url(#mask116_77)">
		<g filter="url(#filter_116_79_dd)">
			<rect id="炫光" x="52.118408" y="37.000000" width="174.000000" height="8.000000" fill="#3BD2E9" fill-opacity="1.000000"/>
			<rect id="炫光" x="52.693359" y="37.575012" width="172.850006" height="6.850000" stroke="#979797" stroke-opacity="0" stroke-width="1.150000"/>
		</g>
	</g>
	<g mask="url(#mask116_77)">
		<g filter="url(#filter_116_80_dd)">
			<path id="三角形" d="M27.51 21.03L25.72 21.88C24.95 22.25 24.07 21.69 24.07 20.85L24.07 19.15C24.07 18.3 24.95 17.74 25.72 18.11L27.51 18.96C28.39 19.37 28.39 20.62 27.51 21.03Z" fill="#FDBD00" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="三角形" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
		</g>
	</g>
	<g mask="url(#mask116_77)">
		<g filter="url(#filter_116_81_dd)">
			<path id="三角形备份" d="M242.51 21.03L244.3 21.88C245.07 22.25 245.95 21.69 245.95 20.85L245.95 19.15C245.95 18.3 245.07 17.74 244.3 18.11L242.51 18.96C241.63 19.37 241.63 20.62 242.51 21.03Z" fill="#FDBD00" fill-opacity="1.000000" fill-rule="evenodd"/>
			<path id="三角形备份" d="" fill="#979797" fill-opacity="0" fill-rule="evenodd"/>
		</g>
	</g>
</svg>
