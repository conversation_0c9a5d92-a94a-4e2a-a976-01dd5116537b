// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/GasNode/delete */
export async function deleteUsingDelete7(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE7Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasNode/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/GasNode/page */
export async function pageUsingGet3(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGET3Params,
  options?: { [key: string]: any },
) {
  return request<API.PageGasNode>('/masc/GasNode/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/GasNode/save */
export async function saveUsingPost7(
  body: API.GasNode,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasNode/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/GasNode/update */
export async function updateUsingPut7(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT7Params,
  body: API.GasNode,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasNode/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/GasNode/upload */
export async function uploadUsingPost5(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasNode/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
