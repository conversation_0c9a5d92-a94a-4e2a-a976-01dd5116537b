import CustomButton from '@/components/CustomButton';
import CustomModal from '@/components/CustomModal';
import CustomTable from '@/components/CustomTable';
import {
  deleteUsingDELETE15,
  pageUsingGET9,
  saveUsingPOST15,
  updateUsingPUT15,
  uploadUsingPOST15,
} from '@/services/homepage/shouyexinnengyuanchuli';
import px from '@/utils/px';
import { CloudUploadOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Space,
  Upload,
} from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.sass';

interface Props {
  name?: string;
  infoOpen: boolean;
  onCancelInfo: () => void;
}

interface TableRecord {
  genNumber: number;
  id: number;
  [key: string]: any; // for hour1-hour24
}

const Index: React.FC<Props> = (props: Props) => {
  const { infoOpen, onCancelInfo } = props;
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<TableRecord | null>(null);
  const [tableData, setTableData] = useState<TableRecord[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();

  const fetchTableData = async (page: number = 1) => {
    try {
      const response = await pageUsingGET9({
        current: page,
        size: 6,
      });
      if (response.code === 1000) {
        setTableData(response.data.records);
        setTotalRecords(response.data.total);
      }
    } catch (error) {
      message.error('获取数据失败');
    }
  };

  useEffect(() => {
    if (infoOpen) {
      fetchTableData();
    }
  }, [infoOpen]);

  const handleEdit = (record: TableRecord) => {
    setCurrentRecord(record);
    const formValues = {
      unitNumber: record.genNumber,
      ...Object.fromEntries(
        Array.from({ length: 24 }, (_, i) => [
          `t${i + 1}`,
          record[`hour${i + 1}`],
        ]),
      ),
    };
    form.setFieldsValue(formValues);
    setEditModalVisible(true);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const apiValues = {
        id: currentRecord?.id,
        genNumber: values.unitNumber,
        ...Object.fromEntries(
          Array.from({ length: 24 }, (_, i) => [
            `hour${i + 1}`,
            values[`t${i + 1}`],
          ]),
        ),
      };

      const response = await updateUsingPUT15(
        { id: currentRecord?.id },
        apiValues,
      );

      if (response.code === 1000) {
        message.success('保存成功');
        setEditModalVisible(false);
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('Save failed:', error);
      message.error('保存失败');
    }
  };

  const handleDelete = async (record: TableRecord) => {
    try {
      const response = await deleteUsingDELETE15({
        id: record.id,
      });

      if (response.code === 1000) {
        message.success('删除成功');
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('Delete failed:', error);
      message.error('删除失败');
    }
  };

  const handleAddSave = async () => {
    try {
      const values = await addForm.validateFields();
      const apiValues = {
        genNumber: Number(values.unitNumber),
        ...Object.fromEntries(
          Array.from({ length: 24 }, (_, i) => [
            `hour${i + 1}`,
            Number(values[`t${i + 1}`]),
          ]),
        ),
      };

      const response = await saveUsingPOST15(apiValues);

      if (response.code === 1000) {
        message.success('添加成功');
        setAddModalVisible(false);
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '添加失败');
      }
    } catch (error) {
      console.error('Add failed:', error);
      message.error('添加失败');
    }
  };

  const handleUpload = async (file: RcFile) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await uploadUsingPOST15(formData);

      if (response.code === 1000) {
        message.success('上传成功');
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '上传失败');
      }
      return false;
    } catch (error) {
      console.error('Upload failed:', error);
      message.error('上传失败');
      return false;
    }
  };

  const handleAdd = () => {
    addForm.resetFields();
    setAddModalVisible(true);
  };

  const columns = [
    {
      title: '机组编号',
      dataIndex: 'genNumber',
      key: 'genNumber',
      width: 80,
      fixed: 'left',
    },
    ...Array.from({ length: 24 }, (_, i) => ({
      title: String(i + 1),
      dataIndex: `hour${i + 1}`,
      key: `hour${i + 1}`,
    })),
    {
      title: '操作',
      key: 'operation',
      fixed: 'right',
      width: 120,
      render: (_: any, record: TableRecord) => (
        <Space>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const formItems = (
    <Row gutter={8}>
      <Col span={4}>
        <Form.Item
          label="机组编号"
          name="unitNumber"
          rules={[{ required: true, message: '请输入机组编号' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      {Array.from({ length: 24 }, (_, i) => i + 1).map((hour) => (
        <Col span={4} key={hour}>
          <Form.Item
            label={`${hour}时`}
            name={`t${hour}`}
            rules={[{ required: true, message: `请输入${hour}时数据` }]}
          >
            <Input />
          </Form.Item>
        </Col>
      ))}
    </Row>
  );

  return (
    <div className={styles.container}>
      <CustomModal
        open={infoOpen}
        title="新能源出力"
        onCancel={onCancelInfo}
        footer={null}
        width={'90vw'}
        className={styles.customModal}
        centered={true}
      >
        <div className={styles.box}>
          <div style={{ display: 'flex' }}>
            <CustomButton icon={<PlusOutlined />} onClick={handleAdd}>
              添加
            </CustomButton>
            <Upload beforeUpload={handleUpload}>
              <CustomButton icon={<CloudUploadOutlined />}>导入</CustomButton>
            </Upload>
          </div>
          <CustomTable
            className={styles.customTable}
            columns={columns}
            dataSource={tableData}
            scroll={{ x: 'max-content' }}
            pagination={{
              current: currentPage,
              pageSize: 6,
              total: totalRecords,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              onChange: (page) => {
                setCurrentPage(page);
                fetchTableData(page);
              },
            }}
          />
        </div>
      </CustomModal>

      {/* 编辑弹窗 */}
      <Modal
        title="编辑节点"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={handleSave}
        width={px(1200)}
      >
        <Form form={form} layout="vertical" initialValues={currentRecord}>
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>

      {/* 添加弹窗 */}
      <Modal
        title="添加节点"
        open={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onOk={handleAddSave}
        width={px(1200)}
      >
        <Form form={addForm} layout="vertical">
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>
    </div>
  );
};

export default Index;
