// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 调用量价风险传导算法V2 GET /masc/VolumeAndPriceRiskTransmission/callMainFunction */
export async function callMainFunctionUsingGET(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.callMainFunctionUsingGETParams,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/VolumeAndPriceRiskTransmission/callMainFunction', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取系统电价（算法运行结果） GET /masc/VolumeAndPriceRiskTransmission/getSystemPrice */
export async function getSystemPriceUsingGET(options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    '/masc/VolumeAndPriceRiskTransmission/getSystemPrice',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}
