@import '@/assets/css/helper.sass'
.sidebarContainer
  display: flex
  flex-direction: row
  align-items: center
 
  height: 100%
 
  
  .sidebar
    margin-left: px(10)
    ul
      list-style: none
      padding: 0
      margin: 0
      color: rgba(216, 240, 255, 0.7)
      font-size: px(30)
      font-weight: 400
      line-height: px(34)
      font-family:  youshe
      li
        padding: px(10) px(0)
        cursor: pointer
        text-align: center
        margin-bottom: px(60)

        &:hover

        &.active
          background-image: url('@/assets/images/activeBg.png')
          background-size: 100% 100%
          width: px(304)
          height:px(41)
          background-repeat: no-repeat
          background-position: center px(15) // 调整背景图片的位置
          color: white
  .divider
    background-image: url('@/assets/images/devider.svg')
    background-repeat: no-repeat
    width: px(2)
    height: px(898)
    margin-left: px(10) // 或者根据需要调整边距
 
 

 