import px from '@/utils/px';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
interface BarChartProps {
  xAxisData: string[];
  seriesData: number[][];
}
const BarChart: React.FC<BarChartProps> = ({ xAxisData, seriesData }) => {
  const chartRef = useRef(null);
  useEffect(() => {
    if (!chartRef.current) {
      return;
    }
    // 初始化图表并指定主题
    const myChart = echarts.init(chartRef.current);
    const option = {
      tooltip: {
        trigger: 'axis',

        textStyle: {
          fontSize: px(16), // 设置 tooltip 中文本的字体大小
        },
      },
      grid: {
        top: '25%', // Add padding to the top of the chart area
        left: '15%',
        right: '13%',
        bottom: '0%',
        containLabel: true,
      },
      legend: {
        data: ['机组1', '机组2', '机组3', '机组4'],
        textStyle: {
          color: '#fff', // Set legend label color to white
          fontSize: px(16),
        },
      },
      xAxis: [
        {
          name: ' ',
          nameTextStyle: {
            color: '#fff', // Set y-axis label color to white
            fontSize: px(16),
          },
          type: 'category',
          data: xAxisData,
          interval: 30, // 控制每个类目之间的间隔
          axisPointer: {
            type: 'shadow',
          },
          axisLabel: {
            color: '#fff', // Set x-axis label color to white
            fontSize: px(16),
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: ' ',
          nameTextStyle: {
            color: '#fff', // Set y-axis label color to white
            fontSize: px(16),
          },
          axisLabel: {
            formatter: '{value}',
            color: '#fff', // Set y-axis label color to white
            fontSize: px(16),
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#304A65',
              type: 'dashed',
            },
          },
        },
      ],
      series: [
        {
          name: '机组1',
          type: 'line',
          itemStyle: {
            color: '#1EC2FF',
          },
          data: seriesData[0],
        },
        {
          name: '机组2',
          type: 'line',

          data: seriesData[1],
        },
        {
          name: '机组3',
          type: 'line',
          data: seriesData[2],
        },
        {
          name: '机组4',
          type: 'line',
          data: seriesData[3],
        },
      ],
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100,
        },
      ],
    };

    myChart.setOption(option);
    return () => {
      myChart.dispose();
    };
  }, [xAxisData, seriesData]);

  return <div ref={chartRef} style={{ height: '100%', width: '100%' }} />;
};
export default BarChart;
