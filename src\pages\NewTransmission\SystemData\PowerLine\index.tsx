import ChartTitle from '@/components/ui/chartTitle';
import _interface from '@/services/interface';
import px from '@/utils/px';
import {
  Button,
  Empty,
  Form,
  Input,
  Popconfirm,
  Space,
  Table,
  Upload,
  message,
} from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.sass';

const { uploadUsingPost3, pageUsingGet2, deleteUsingDelete4, updateUsingPut4 } =
  _interface.dianlixitongxianlu;

const EditableCell = ({
  editing,
  dataIndex,
  title,
  record,
  children,
  ...restProps
}: any) => {
  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[{ required: true, message: `请输入${title}!` }]}
          initialValue={record[dataIndex]}
        >
          <Input />
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const Index = () => {
  const [dataSource, setDataSource] = useState<API.ElePipe[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingKey, setEditingKey] = useState<string>('');
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 8,
    total: 0,
  });

  const isEditing = (record: any) => record.id === editingKey;

  const fetchData = async (page = 1) => {
    try {
      setLoading(true);
      const response = (await pageUsingGet2({
        current: page,
        size: pagination.pageSize,
      })) as ResponseData<API.PageElePipe>;

      if (response.code === 1000 && response.data?.records) {
        setDataSource(response.data.records);
        setPagination({
          ...pagination,
          current: page,
          total: response.data.total || 0,
        });
      }
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };
  const handleEdit = (record: API.ElePipe) => {
    form.setFieldsValue({
      fbus: record.fbus,
      tbus: record.tbus,
      r: record.r,
      x: record.x,
      b: record.b,
      rateA: record.rateA,
      rateB: record.rateB,
      rateC: record.rateC,
      ratio: record.ratio,
      angle: record.angle,
      status: record.status,
    });
    setEditingKey(record.id);
  };

  const handleCancel = () => {
    setEditingKey('');
  };

  const handleSave = async (record: API.ElePipe) => {
    try {
      const row = await form.validateFields();
      await updateUsingPut4({ id: record.id }, { ...record, ...row });
      message.success('保存成功');
      setEditingKey('');
      fetchData();
    } catch (error) {
      message.error('保存失败');
      console.error('Save failed:', error);
    }
  };

  const handleDelete = async (record: API.ElePipe) => {
    try {
      await deleteUsingDelete4({ id: record.id });
      message.success('删除成功');
      fetchData();
    } catch (error) {
      message.error('删除失败');
      console.error('Delete failed:', error);
    }
  };

  const columns = [
    {
      title: '起始母线',
      dataIndex: 'fbus',
      key: 'fbus',
      editable: true,
    },
    {
      title: '终止母线',
      dataIndex: 'tbus',
      key: 'tbus',
      editable: true,
    },
    {
      title: '电阻',
      dataIndex: 'r',
      key: 'r',
      editable: true,
    },
    {
      title: '电抗',
      dataIndex: 'x',
      key: 'x',
      editable: true,
    },
    {
      title: '电导',
      dataIndex: 'b',
      key: 'b',
      editable: true,
    },
    {
      title: '额定电流A',
      dataIndex: 'rateA',
      key: 'rateA',
      editable: true,
    },
    {
      title: '额定电流B',
      dataIndex: 'rateB',
      key: 'rateB',
      editable: true,
    },
    {
      title: '额定电流C',
      dataIndex: 'rateC',
      key: 'rateC',
      editable: true,
    },
    {
      title: '比率',
      dataIndex: 'ratio',
      key: 'ratio',
      editable: true,
    },
    {
      title: '相角',
      dataIndex: 'angle',
      key: 'angle',
      editable: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      editable: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_: any, record: API.ElePipe) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button type="link" onClick={() => handleSave(record)}>
              保存
            </Button>
            <Button type="link" onClick={handleCancel}>
              取消
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              type="link"
              disabled={editingKey !== ''}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这条记录吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    fetchData();
  }, []);

  const customUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      await uploadUsingPost3(formData as any);
      onSuccess('OK');
      await fetchData();
    } catch (error) {
      onError(error);
      console.error('Upload failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.systemData}>
      <div className={styles.table}>
        <div className={styles.chartTitle}>
          <ChartTitle
            title="线路"
            width={px(600)}
            height={px(37)}
            right={
              <Upload customRequest={customUpload}>
                <Button>上传</Button>
              </Upload>
            }
          />
        </div>
        <div style={{ marginTop: px(20) }}>
          {dataSource.length > 0 ? (
            <Form form={form} component={false}>
              <Table
                components={{
                  body: {
                    cell: EditableCell,
                  },
                }}
                columns={columns.map((col) => {
                  if (!col.editable) {
                    return col;
                  }
                  return {
                    ...col,
                    onCell: (record: API.ElePipe) => ({
                      record,
                      dataIndex: col.dataIndex,
                      title: col.title,
                      editing: isEditing(record),
                    }),
                  };
                })}
                dataSource={dataSource}
                loading={loading}
                rowKey="id"
                pagination={{
                  ...pagination,
                  onChange: (page) => fetchData(page),
                }}
              />
            </Form>
          ) : (
            <Empty description="没有数据，请上传数据" />
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
