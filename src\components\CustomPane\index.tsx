import px from '@/utils/px';
import React, { ReactNode } from 'react';
import styles from './index.sass';

interface Props {
  title: string;
  children: ReactNode;
  button?: ReactNode;
  button2?: ReactNode;
}

const CustomModal: React.FC<Props> = (props) => {
  const { title = '标题', children, button, button2 } = props;

  const renderButton = (
    btn: ReactNode,
    additionalStyle?: React.CSSProperties,
  ) => {
    if (React.isValidElement(btn)) {
      return <div style={additionalStyle}>{btn}</div>;
    }
    return (
      <div className={styles.button} style={additionalStyle}>
        {btn}
      </div>
    );
  };

  return (
    <div className={styles.box}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <div className={styles.title}>
          <div className={styles.titleBg}></div>
          <div>{title}</div>
        </div>
        {button2 && renderButton(button2, { marginRight: px(-140) })}
        {button && renderButton(button)}
      </div>
      <div className={styles.titleB}></div>
      {children}
    </div>
  );
};

export default CustomModal;
