import { request } from '@umijs/max';

// Disaster scenario interface
export interface DisasterScenario {
  number: string;
  name: string;
  toughnessIndex: number;
  evaluationTime: string;
  id: number;
}

// Dispatchable resource capacity interface
export interface DispatchableResourceCapacity {
  name: string;
  value: number;
  percentage: string;
  id: number;
}

// Flexible resource capacity interface
export interface FlexibleResourceCapacity {
  name: string;
  value: number;
  percentage: string;
  id: number;
}

// Get disaster scenario list
export async function getPreDisasterScenario(): Promise<DisasterScenario[]> {
  return request('/masc/PreDisasterScenario/query', {
    method: 'GET',
  });
}

// Get dispatchable resource capacity data
export async function getPreDispatchableResourceCapacity(): Promise<
  DispatchableResourceCapacity[]
> {
  return request('/masc/PreDisasterDispatchableResourceCapacity/query', {
    method: 'GET',
  });
}

// Download dispatchable resource capacity data
export async function downloadPreDispatchableResourceCapacity(): Promise<Blob> {
  return request('/masc/PreDisasterDispatchableResourceCapacity/download', {
    method: 'GET',
    responseType: 'blob',
  });
}

// Upload dispatchable resource capacity data
export async function uploadPreDispatchableResourceCapacity(
  file: File,
): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);

  return request('/masc/PreDisasterDispatchableResourceCapacity/upload', {
    method: 'POST',
    data: formData,
    requestType: 'form',
  });
}

// Get flexible resource capacity data
export async function getPreFlexibleResourceCapacity(): Promise<
  FlexibleResourceCapacity[]
> {
  return request('/masc/PreDisasterFlexibleResourceCapacity/query', {
    method: 'GET',
  });
}

// Download flexible resource capacity data
export async function downloadPreFlexibleResourceCapacity(): Promise<Blob> {
  return request('/masc/PreDisasterFlexibleResourceCapacity/download', {
    method: 'GET',
    responseType: 'blob',
  });
}

// Upload flexible resource capacity data
export async function uploadPreFlexibleResourceCapacity(
  file: File,
): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);

  return request('/masc/PreDisasterFlexibleResourceCapacity/upload', {
    method: 'POST',
    data: formData,
    requestType: 'form',
  });
}

// 储能运行计划数据接口
export interface EnergyStorageOperationPlan {
  hours: number[];
  values: number[];
}

// 获取储能运行计划数据
export async function getPreDisasterEnergyStorageOperationPlan(): Promise<EnergyStorageOperationPlan> {
  return request('/masc/PreDisasterEnergyStorageOperationPlan/query', {
    method: 'GET',
  });
}

// 燃气机组备用数据接口
export interface GasUnitReserve {
  hours: number[];
  values: number[];
}

// 获取燃气机组备用数据
export async function getPreDisasterGasUnitReserve(): Promise<GasUnitReserve> {
  return request('/masc/PreDisasterGasUnitReserve/query', {
    method: 'GET',
  });
}

// 灵活资源指标数据接口
export interface FlexResourceMetric {
  name: string;
  value: string;
  id: number;
}

// 获取灵活资源指标数据
export async function getPreDisasterFlexResourceMetrics(): Promise<
  FlexResourceMetric[]
> {
  return request('/masc/PreDisasterFlexResourceMetrics/query', {
    method: 'GET',
  });
}

// 故障影响数据接口
export interface FaultImpact {
  name: string;
  value: string;
  id: number;
}

// 获取故障影响数据
export async function getPostDisasterFaultImpact(): Promise<FaultImpact[]> {
  return request('/masc/PostDisasterFaultImpact/query', {
    method: 'GET',
  });
}

// 负荷曲线数据接口
export interface LoadCurve {
  hours: number[];
  values: number[];
}

// 获取负荷曲线数据
export async function getPostDisasterLoad(): Promise<LoadCurve> {
  return request('/masc/PostDisasterLoad/query', {
    method: 'GET',
  });
}

// 下载负荷曲线数据
export async function downloadPostDisasterLoad(): Promise<Blob> {
  return request('/masc/PostDisasterLoad/download', {
    method: 'GET',
    responseType: 'blob',
  });
}

// 上传负荷曲线数据
export async function uploadPostDisasterLoad(file: File): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);

  return request('/masc/PostDisasterLoad/upload', {
    method: 'POST',
    data: formData,
    requestType: 'form',
  });
}

// 风电出力曲线数据接口
export interface WindOutput {
  hours: number[];
  values: number[];
}

// 获取风电出力曲线数据
export async function getPostDisasterWindOutput(): Promise<WindOutput> {
  return request('/masc/PostDisasterWindOutput/query', {
    method: 'GET',
  });
}

// 下载风电出力曲线数据
export async function downloadPostDisasterWindOutput(): Promise<Blob> {
  return request('/masc/PostDisasterWindOutput/download', {
    method: 'GET',
    responseType: 'blob',
  });
}

// 上传风电出力曲线数据
export async function uploadPostDisasterWindOutput(file: File): Promise<any> {
  const formData = new FormData();
  formData.append('file', file);

  return request('/masc/PostDisasterWindOutput/upload', {
    method: 'POST',
    data: formData,
    requestType: 'form',
  });
}
