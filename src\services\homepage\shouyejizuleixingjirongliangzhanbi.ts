// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/GensetTypeAndCapacity/delete */
export async function deleteUsingDELETE13(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE13Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GensetTypeAndCapacity/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 查询全部 GET /masc/GensetTypeAndCapacity/findAll */
export async function findAllUsingGET(options?: { [key: string]: any }) {
  return request<API.GensetTypeAndCapacity[]>(
    '/masc/GensetTypeAndCapacity/findAll',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 单条插入 POST /masc/GensetTypeAndCapacity/save */
export async function saveUsingPOST13(
  body: API.GensetTypeAndCapacity,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GensetTypeAndCapacity/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/GensetTypeAndCapacity/update */
export async function updateUsingPUT13(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT13Params,
  body: API.GensetTypeAndCapacity,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GensetTypeAndCapacity/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/GensetTypeAndCapacity/upload */
export async function uploadUsingPOST13(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GensetTypeAndCapacity/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
