declare namespace API {
  type callMainFunctionUsingGETParams = {
    /** kc */
    kc?: number;
  };
  type CoalElectricityPrice = {
    coalElectricityPrice?: number;
    date?: number;
    id?: number;
  };

  type CoalPrice = {
    coalPrice?: number;
    date?: number;
    id?: number;
  };

  type deleteUsingDELETE1Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE2Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE3Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE4Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE5Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE6Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE7Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE8Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETEParams = {
    /** id */
    id: number;
  };

  type EleGenset = {
    id?: number;
    mbase?: number;
    number?: number;
    pg?: number;
    pmax?: number;
    pmin?: number;
    qg?: number;
    qmax?: number;
    qmin?: number;
    status?: number;
    type?: string;
    vg?: number;
  };

  type EleNode = {
    area?: number;
    baseKv?: number;
    bs?: number;
    busI?: number;
    gs?: number;
    id?: number;
    pd?: number;
    qd?: number;
    type?: number;
    va?: number;
    vm?: number;
    vmax?: number;
    vmin?: number;
    zone?: number;
  };

  type ElePipe = {
    angle?: number;
    b?: number;
    fbus?: number;
    id?: number;
    r?: number;
    rateA?: number;
    rateB?: number;
    rateC?: number;
    ratio?: number;
    status?: number;
    tbus?: number;
    x?: number;
  };

  type GasElectricityPrice = {
    gasElectricityPrice?: number;
    hour?: number;
    id?: number;
  };

  type GasGensetCoefficient = {
    a?: number;
    b?: number;
    c?: number;
    id?: number;
  };

  type GasNode = {
    id?: number;
    maxPressure?: number;
    minPressure?: number;
    nodeLoad?: number;
    nodeNo?: number;
  };

  type GasPipe = {
    c?: number;
    diameter?: number;
    fnode?: number;
    id?: number;
    length?: number;
    m2?: number;
    pipe?: number;
    tnode?: number;
  };

  type OrderItem = {
    asc?: boolean;
    column?: string;
  };

  type PageEleGenset = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: EleGenset[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageEleNode = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: EleNode[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageElePipe = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: ElePipe[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageGasNode = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: GasNode[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageGasPipe = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: GasPipe[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type pageUsingGET1Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET2Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET3Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET4Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGETParams = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type updateUsingPUT1Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT2Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT3Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT4Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT5Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT6Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT7Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT8Params = {
    /** id */
    id: number;
  };

  type updateUsingPUTParams = {
    /** id */
    id: number;
  };
}
