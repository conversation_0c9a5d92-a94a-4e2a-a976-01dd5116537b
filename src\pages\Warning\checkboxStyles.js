// checkboxStyles.js
export const checkboxStyleObject = {
  '.chakra-checkbox__control': {
    width: '18px',
    height: '18px',
    bg: 'transparent',
    borderColor: 'gray',
    borderWidth: '1px',
    color: 'gray',
    svg: {
      width: '12px',
      height: '12px',
    },
    '&::before': {
      display: 'none',
    },
    '&[data-checked]': {
      bg: 'transparent',
      color: '#FDBD00',
      borderColor: '#FDBD00',
    },
    '&:hover': {
      bg: 'transparent',
      borderColor: '#FDBD00',
    },
    '&:not([data-checked]):hover': {
      bg: 'transparent',
      borderColor: '#FDBD00',
    },
  },
};
