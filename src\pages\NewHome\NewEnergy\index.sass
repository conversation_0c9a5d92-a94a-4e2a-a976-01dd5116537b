@import "~@/assets/css/helper.sass"
.box
  width: 100%
  height: px(800)
  display: flex
  flex-direction: column
  overflow: hidden
 
  .customModal
    height: fit-content
    width: fit-content
  .customTable
    flex: 1
    font-size: px(14)
    overflow: auto
 
    :global
      .ant-table-wrapper
        height: 100%
        width: 100%
      .ant-spin-nested-loading
        height: 100%
        width: 100%
      .ant-spin-container
        height: 100%
        width: 100%
        display: flex
        flex-direction: column
      .ant-table
        flex: 1
        overflow: auto
        width: 100%
      .ant-table-container
        height: 100%
        min-width: 100%
      .ant-table-content
        width: 100%
      .ant-table-body
        max-height: calc(100% - px(55)) !important
        overflow-y: auto !important
        overflow-x: auto !important
        width: 100%

.formContainer
  :global
    // Modal 标题
    .ant-modal-title
      font-size: px(14)
    
    // Form 相关元素
    .ant-form-item-label > label
      font-size: px(14)
    
    .ant-input
      font-size: px(14)
    
    .ant-form-item-explain
      font-size: px(14)
    
    // Modal footer 按钮
    .ant-btn
      font-size: px(14)
    
    .ant-form-item
      margin-bottom: px(8)
    
    .ant-row
      display: flex
      flex-wrap: wrap
      margin: 0 px(-4)
      
      .ant-col
        flex: 0 0 20%
        padding: 0 px(4)
        
        @media screen and (max-width: px(1200))
          flex: 0 0 25%
          
        @media screen and (max-width: px(992))
          flex: 0 0 33.33%
          
        @media screen and (max-width: px(768))
          flex: 0 0 50%
          
        @media screen and (max-width: px(576))
          flex: 0 0 100%

// 添加全局按钮样式
:global
  .ant-btn
    font-size: px(18)
    &.ant-btn-sm
      font-size: px(14)
    &.ant-btn-lg
      font-size: px(20)

   