import Sidebar from '@/components/SideBar';
import { useModel } from '@umijs/max';
import styles from './index.sass';
import Result from './Result';
import RunningData from './RunningData';
import SystemPage from './SystemData';

export default function Page() {
  const { key, setKey } = useModel('menu');
  console.log(key, 'key');
  const menuList = [
    {
      key: 3,
      label: '电力系统节点',
    },
    {
      key: 4,
      label: '电力系统线路',
    },
    {
      key: 5,
      label: '运行数据',
    },
    {
      key: 6,
      label: '结果展示',
    },
  ];

  return (
    <div className={styles.container}>
      <Sidebar menu={menuList} activeId={key} onChange={setKey} />
      <div className={styles.content}>
        <SystemPage />
        <RunningData />
        <Result />
      </div>
    </div>
  );
}
