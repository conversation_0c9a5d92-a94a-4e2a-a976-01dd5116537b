import { useModel } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Menu } from 'antd';
import React, { useState } from 'react';
import styles from './index.sass';
type MenuItem = Required<MenuProps>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group',
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
    type,
  } as MenuItem;
}
//实现多级菜单栏
const items: MenuProps['items'] = [
  getItem('系统数据', 'sub1', <></>, [
    // getItem(
    //   '天然气系统',
    //   'g1',
    //   null,
    //   [getItem('节点', '1'), getItem('管道', '2')],
    //   'group',
    // ),
    getItem(
      '电力系统',
      'g2',
      null,
      [getItem('节点', '3'), getItem('线路', '4')],
      'group',
    ),
  ]),
  getItem('运行数据', '5', <></>),
  getItem('结果展示', '6', <></>),
];

//父级菜单
const rootSubmenuKeys = ['sub1', 'sub2', 'sub3'];

const App: React.FC = () => {
  const { setKey } = useModel('menu');
  const [openKeys, setOpenKeys] = useState(['sub1']);
  //点击菜单，收起其他展开的所有菜单，保持菜单聚焦简洁
  const onOpenChange: MenuProps['onOpenChange'] = (keys) => {
    const latestOpenKey = keys.find((key) => openKeys.indexOf(key) === -1);
    if (latestOpenKey && rootSubmenuKeys.indexOf(latestOpenKey!) === -1) {
      setOpenKeys(keys);
    } else {
      setOpenKeys(latestOpenKey ? [latestOpenKey] : []);
    }
  };
  //点击菜单项切换展示
  const onClick: MenuProps['onClick'] = (e) => {
    const key = Number(e.key);
    setKey(key);
  };

  return (
    <div className={styles.box}>
      <Menu
        onOpenChange={onOpenChange}
        openKeys={openKeys}
        mode="inline"
        items={items}
        onClick={onClick}
        defaultSelectedKeys={['1']}
      />
    </div>
  );
};

export default App;
