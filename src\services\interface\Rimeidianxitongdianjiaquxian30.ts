// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/CoalElectricityPrice/delete */
export async function deleteUsingDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETEParams,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/CoalElectricityPrice/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/CoalElectricityPrice/save */
export async function saveUsingPost(
  body: API.CoalElectricityPrice,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/CoalElectricityPrice/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询一个月 GET /masc/CoalElectricityPrice/selectOneMonth */
export async function selectOneMonthUsingGet(options?: { [key: string]: any }) {
  return request<number[]>('/masc/CoalElectricityPrice/selectOneMonth', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/CoalElectricityPrice/update */
export async function updateUsingPut(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUTParams,
  body: API.CoalElectricityPrice,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/CoalElectricityPrice/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}
