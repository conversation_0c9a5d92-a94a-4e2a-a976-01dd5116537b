@import "~@/assets/css/helper.sass"
.box
    :global
        .ant-picker
            width: px(230)
            padding: px(3) px(10) !important
            background: rgba(0,0,0,0)
            border: white solid px(1)
            line-height: 0
            height: px(34) !important
        .ant-picker-input >input,.ant-picker-separator,.ant-picker-suffix,
        .ant-picker .ant-picker-input >input::placeholder,
        .ant-select-single .ant-select-selector,
        .ant-select .ant-select-arrow,
        .ant-select-selection-item
            color: white !important
            font-size: px(14)
        .ant-select-selector,.ant-select-single .ant-select-selector
            background: rgba(0,0,0,0)
            border: white solid px(1)
        .ant-select-selector
            padding: px(3) px(10) !important
        // line-height: px(14) !important
            height: px(34) !important
        .ant-select-selection-item,.ant-select-selection-placeholder
            font-size: px(14)
            line-height: px(24) !important
        .ant-select-arrow
            margin-top: px(12)
            top: 0
        .ant-select-dropdown
            .ant-select-item
                color: white !important
                font-size: px(14) !important
                &:hover
                    background: rgba(255,255,255,0.1)
                &-option-selected
                    background: rgba(255,255,255,0.2)
.select
    min-width: px(150)
    height: px(34)
