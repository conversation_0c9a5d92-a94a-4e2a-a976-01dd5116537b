// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/GasGen/delete */
export async function deleteUsingDELETE9(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE9Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasGen/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/GasGen/page */
export async function pageUsingGET6(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGET6Params,
  options?: { [key: string]: any },
) {
  return request<API.PageGasGen>('/masc/GasGen/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/GasGen/save */
export async function saveUsingPOST9(
  body: API.GasGen,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasGen/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/GasGen/update */
export async function updateUsingPUT9(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT9Params,
  body: API.GasGen,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasGen/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/GasGen/upload */
export async function uploadUsingPOST8(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasGen/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
