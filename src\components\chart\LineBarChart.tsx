import px from '@/utils/px';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';

interface LineBarChartProps {
  category: string[];
  lineData: number[];
  barData: number[];
  xlabelName: string;
  ylabelName: string;
}

const LineBarChart: React.FC<LineBarChartProps> = ({
  category,
  lineData,
  barData,
  xlabelName,
  ylabelName,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (chartRef.current) {
      const chart = echarts.init(chartRef.current);
      const option = {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        tooltip: {
          textStyle: {
            fontSize: px(18),
          },
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          data: ['line', 'bar'],
          textStyle: {
            color: '#ccc',
            fontSize: px(20),
          },
        },
        xAxis: {
          nameTextStyle: {
            fontSize: px(16),
            align: 'left', // Center-align the yAxis label
            color: 'rgba(216, 240, 255, 0.8)',
          },
          axisLabel: {
            fontSize: px(16),
          },
          data: category,
          name: xlabelName,
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
        },
        yAxis: {
          name: ylabelName,

          nameTextStyle: {
            fontSize: px(16),
            align: 'right', // Center-align the yAxis label
            color: 'rgba(216, 240, 255, 0.8)',
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(176, 215, 255, 0.25)', // Set the color of yAxis line
              type: 'dashed', // Set the yAxis line style to dashed
            },
          },
          axisLabel: {
            fontSize: px(16), // 设置y轴标签文字大小
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(216, 240, 255, 0.8)',
            },
          },
        },
        series: [
          {
            name: 'line',
            type: 'line',
            smooth: true,
            symbol: 'false',
            itemStyle: {
              color: '#1978E5',
            },
            data: lineData,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(0, 150, 255, 0.5)',
                },
                {
                  offset: 1,
                  color: 'rgba(0, 150, 255, 0)',
                },
              ]),
            },
          },
          //   {
          //     name: 'bar',
          //     type: 'bar',
          //     barWidth: 10,
          //     itemStyle: {
          //       borderRadius: 5,
          //       color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //         { offset: 0, color: '#14c8d4' },
          //         { offset: 1, color: '#43eec6' },
          //       ]),
          //     },
          //     data: barData,
          //   },
          {
            name: 'line',
            type: 'bar',
            barGap: '-100%',
            barWidth: px(21),
            itemStyle: {
              color: 'rgb(255, 255, 255,0.1)',
            },
            z: 12,
            data: lineData,
          },
          {
            name: 'dotted',
            type: 'pictorialBar',
            symbol: 'rect',
            itemStyle: {
              color: '#1978E5',
            },
            symbolRepeat: true,
            symbolSize: [21, 8],
            symbolMargin: 4,
            z: 10,
            data: barData,
          },
        ],
      };
      chart.setOption(option);
      return () => chart.dispose();
    }
  }, [category, lineData, barData]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }}></div>;
};

export default LineBarChart;
