import React, { useState } from 'react';
import ReactECharts from 'echarts-for-react';
import { CloudUploadOutlined, UploadOutlined, FileAddOutlined, SettingOutlined, LineChartOutlined } from '@ant-design/icons';
import './index.sass';
import px from '@/utils/px';

const PowerLoadForecast: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('工业');

  const hours = ['0:15', '0:30', '0:45', '1:00', '1:15', '1:30', '1:45', '2:00', '2:15', '2:30', '2:45', '3:00',
                '3:15', '3:30', '3:45', '4:00', '4:15', '4:30', '4:45', '5:00', '5:15', '5:30', '5:45', '6:00',
                '6:15', '6:30', '6:45', '7:00', '7:15', '7:30', '7:45', '8:00', '8:15', '8:30', '8:45', '9:00',
                '9:15', '9:30', '9:45', '10:00', '10:15', '10:30', '10:45', '11:00', '11:15', '11:30', '11:45', '12:00',
                '12:15', '12:30', '12:45', '13:00', '13:15', '13:30', '13:45', '14:00', '14:15', '14:30', '14:45', '15:00',
                '15:15', '15:30', '15:45', '16:00', '16:15', '16:30', '16:45', '17:00', '17:15', '17:30', '17:45', '18:00',
                '18:15', '18:30', '18:45', '19:00', '19:15', '19:30', '19:45', '20:00', '20:15', '20:30', '20:45'];

  const forecastOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(10, 20, 40, 0.8)',
      borderColor: 'rgba(0, 174, 255, 0.5)',
      textStyle: {
        color: '#fff'
      },
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: 'rgba(106, 255, 235, 0.5)'
        }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      containLabel: true,
      top: '15%'
    },
    xAxis: {
      type: 'category',
      data: hours,
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        interval: 2
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.05)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '功率（万千瓦时）',
      nameTextStyle: {
        color: 'rgba(255, 255, 255, 0.6)',
        padding: [0, 0, 0, -40]
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.05)'
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(255, 255, 255, 0.6)',
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '预测负荷',
        type: 'line',
        data: [7879.5, 7838.3, 7802.4, 7805.7, 7717.9, 7734.9, 7681.2, 7725.6, 7787.9, 7744, 7578.2, 7623.2,
               7700.3, 7726.5, 7700.4, 7808.5, 7876.2, 7927.5, 8171, 8269.7, 8349.9, 8491.6, 8727.5, 8773.1,
               9221.1, 9278.1, 9607.8, 9704.4, 9883.3, 9953.3, 10090, 10141, 10151, 10126, 10004, 9995.4,
               10028, 9767.8, 9217.2, 9266.5, 9391.9, 9490.1, 10189, 10103, 9953.8, 9922.3, 10027, 10080,
               9826.3, 9864.1, 9698.8, 9612.7, 9713.3, 9698.6, 9659.4, 9707.4, 9709.1, 9767.2, 9746.7, 9739.6,
               9475.1, 9383.6, 8990.2, 9162.2, 9269.5, 9345.7, 9534.6, 9648.2, 9641.5, 9681.3, 9704.1, 9712.2,
               9718.5, 9659, 9572, 9548.5, 9534.3, 9565.9, 9668.5, 9580.1, 9375.3, 9300.5, 8972.1],
        smooth: true,
        lineStyle: {
          width: 4,
          color: '#6affeb'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#6affeb'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(106, 255, 235, 0.4)' },
              { offset: 1, color: 'rgba(106, 255, 235, 0.05)' }
            ]
          }
        }
      }
    ]
  };

  const actualOption = {
    ...forecastOption,
    series: [
      {
        name: '实际负荷',
        type: 'line',
        data: [8237, 8186, 8099, 7913, 7885, 7925, 7890, 7792, 7814, 7791, 7687, 7700,
               7649, 7688, 7744, 7639, 7563, 7605, 7634, 7636, 7612, 7762, 7774, 7838,
               7988, 8128, 8210, 8420, 8491, 8584, 9081, 9189, 9365, 9544, 9796, 9993,
               10001, 10188, 10173, 10207, 10157, 10179, 10190, 9727, 9624, 9564, 9648, 9775,
               10173, 10117, 10099, 10054, 10142, 10152, 9970, 10032, 9927, 9857, 9908, 9857,
               9859, 9907, 9872, 9960, 9877, 9884, 9622, 9451, 9292, 9402, 9468, 9593,
               9591, 9668, 9717, 9769, 9742, 9789, 9831, 9726, 9743, 9703, 9667],
        smooth: true,
        lineStyle: {
          width: 4,
          color: '#00aeff'
        },
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: '#00aeff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 174, 255, 0.4)' },
              { offset: 1, color: 'rgba(0, 174, 255, 0.05)' }
            ]
          }
        }
      }
    ]
  };

  return (
    <div className="powerLoadForecast">
      <div className="gridOverlay"></div>
      <div className="glow glow1"></div>
      <div className="glow glow2"></div>
      
      <div className="container">
        {/* 左侧边栏 */}
        <div className="sidebar">
          <div className="tabs">
            {['工业', '商业', '居民'].map((tab) => (
              <div
                key={tab}
                className={`tab ${activeTab === tab ? 'active' : ''}`}
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </div>
            ))}
          </div>
          
          <div className="section">
            <div className="sectionTitle">
              <UploadOutlined style={{ fontSize: px(22) }} />
              <div></div>
              <div style={{fontWeight:'bold'}}>
              <span>历史负荷数据</span>
              </div>
            </div>
            <div className="uploadArea">
              <div className="uploadIcon">
                <CloudUploadOutlined style={{ fontSize: px(22) }} />
              </div>
              <div className="uploadText">点击上传数据文件</div>
              <div className="uploadSubtext">支持CSV、Excel格式</div>
            </div>
          </div>
          
          <div className="section">
            <div className="sectionTitle">
              <FileAddOutlined style={{ fontSize: px(22) }} />
              <div style={{fontWeight:'bold'}}>
              <span>历史气温数据</span>
              </div>
            </div>
            <div className="uploadArea">
              <div className="uploadIcon">
                <CloudUploadOutlined style={{ fontSize: px(22) }} />
              </div>
              <div className="uploadText">点击上传数据文件</div>
              <div className="uploadSubtext">支持CSV、Excel格式</div>
            </div>
          </div>
          
          <div className="section">
            <div className="sectionTitle">
              <SettingOutlined style={{ fontSize: px(22) }} />
              <div style={{fontWeight:'bold'}}>
              <span>预测参数设置</span>
              </div>
            </div>
            
            <div className="inputGroup">
              <label>预测时段气温</label>
              <div className="inputWrapper">
                <input type="number" defaultValue="28" />
                <div className="units">摄氏度</div>
              </div>
            </div>
            
            <div className="inputGroup">
              <label>预测日期</label>
              <input type="text" defaultValue="2025/3/10" placeholder="yyyy/mm/dd" />
            </div>
            
            <div className="inputGroup">
              <label>是否为节假日</label>
              <select>
                <option value="no">否</option>
                <option value="yes">是</option>
              </select>
            </div>
          </div>
        </div>
        
        {/* 右侧主内容区 */}
        <div className="mainContent">
          {/* 预测负荷图表 */}
          <div className="chartContainer">
            <div className="chartTitle">
              <LineChartOutlined style={{ fontSize: px(22) }} />
              <div style={{fontWeight:'bold', fontSize:px(20)}}>
              <span>预测负荷曲线</span>
              </div>
            </div>
            <ReactECharts 
              option={forecastOption} 
              style={{ height: '100%', width: '100%' }}
              className="chart"
            />
          </div>
          
          {/* 实际负荷图表 */}
          <div className="chartContainer">
            <div className="chartTitle">
              <LineChartOutlined style={{ fontSize: px(22) }} />
              <div style={{fontWeight:'bold', fontSize:px(20)}}>
              <span>实际负荷曲线</span>
              </div>
            </div>
            <ReactECharts 
              option={actualOption} 
              style={{ height: '100%', width: '100%' }}
              className="chart"
            />
          </div>
          
          <div className="statsContainer">
            <div className="statCard">
              <div className="statTitle">峰值 (万千瓦时)</div>
              <div className="statValue">10189
              </div>
            </div>
            <div className="statCard">
              <div className="statTitle">峰谷差 (万千瓦时)</div>
              <div className="statValue">2610.8
              </div>
            </div>
            <div className="statCard">
              <div className="statTitle">平均值 (万千瓦时)</div>
              <div className="statValue">9166.50
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PowerLoadForecast;