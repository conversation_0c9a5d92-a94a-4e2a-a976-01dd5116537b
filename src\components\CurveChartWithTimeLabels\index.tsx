import px from '@/utils/px';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';

interface CurveChartWithTimeLabelsProps {
  data: number[];
  timeLabels?: string[]; // 新增时间标签属性
  yAxisName?: string;
  legendName?: string;
  color?: string;
  height?: number;
}

const CurveChartWithTimeLabels: React.FC<CurveChartWithTimeLabelsProps> = ({
  data,
  timeLabels,
  yAxisName = ' ',
  legendName = ' ',
  color = '#6affeb',
  height = px(240),
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const myChart = echarts.init(chartRef.current);

    const hexToRgb = (hexColor: string) => {
      const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
      const processedHex = hexColor.replace(
        shorthandRegex,
        (m, r, g, b) => r + r + g + g + b + b,
      );

      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(
        processedHex,
      );
      return result
        ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16),
          }
        : null;
    };

    // X轴时间数据 - 使用提供的时间标签或生成默认标签
    const xData =
      timeLabels || Array.from({ length: data.length }, (_, i) => `${i}时`);

    const option = {
      legend: {
        show: true,
        data: [legendName],
        right: '10%',
        top: '0%',
        textStyle: {
          fontSize: px(14),
          color: 'white',
        },
        itemStyle: {
          borderColor: 'transparent',
        },
      },
      series: [
        {
          name: legendName,
          data: data,
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbolSize: 10,
          emphasis: { focus: 'series' },
          animationDuration: 2500,
          animationEasing: 'cubicInOut',
          lineStyle: {
            width: 2,
            color: color,
          },
          areaStyle: {
            width: 4,
            opacity: 0.25,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0.389, color: color },
                {
                  offset: 1,
                  color: `rgba(${hexToRgb(color)?.r},${hexToRgb(color)?.g},${
                    hexToRgb(color)?.b
                  },0)`,
                },
              ],
              global: false,
            },
          },
          itemStyle: {
            color: color,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
        },
        textStyle: {
          color: '#fafafa',
        },
        borderColor: 'transparent',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        extraCssText: 'backdrop-filter: blur(6px);',
      },
      grid: {
        top: '25%',
        left: '3%',
        right: '2.5%',
        bottom: '0%', // 进一步减少底部空间
        containLabel: true,
        show: true, // 显示网格
        borderColor: 'rgba(255, 255, 255, 0.1)', // 边框颜色
        backgroundColor: 'transparent',
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xData,
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
            width: 1,
          },
        },
        axisTick: { show: true },
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            width: 1,
            type: 'solid',
          },
        },
        axisLabel: {
          color: 'white',
          fontSize: px(12), // 减小字体大小以减少重叠
          // 根据数据点数量动态调整标签显示
          formatter: function (value: string, index: number) {
            // 确保第一个和最后一个标签始终显示
            const isFirstLabel = index === 0;
            const isLastLabel = index === xData.length - 1;

            // 固定显示约10个标签
            const interval = Math.max(1, Math.floor(xData.length / 10));

            // 如果是第一个、最后一个或者按间隔计算应该显示的标签
            return isFirstLabel || isLastLabel || index % interval === 0
              ? value
              : '';
          },
          rotate: 30, // 使用30度旋转，提高可读性同时节省空间
          align: 'right', // 右对齐文本，使日期部分更靠近刻度线
          verticalAlign: 'middle', // 垂直居中
          margin: 10, // 增加边距
          interval: 0, // 强制显示所有标签，然后通过formatter来控制
        },
      },
      yAxis: {
        name: yAxisName,
        type: 'value',
        nameLocation: 'end',
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLabel: {
          color: 'white',
          fontSize: px(14),
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)',
            width: 1,
          },
        },
        axisTick: { show: true },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)',
            width: 1,
            type: 'solid',
          },
        },
      },
    };

    myChart.setOption(option);

    return () => {
      myChart.dispose();
    };
  }, [data, timeLabels, yAxisName, legendName, color]);

  return <div ref={chartRef} style={{ width: '100%', height }} />;
};

export default CurveChartWithTimeLabels;
