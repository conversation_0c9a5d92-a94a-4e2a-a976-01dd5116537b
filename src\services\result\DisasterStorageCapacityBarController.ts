// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 导出数据 GET /masc/PostDisasterStorageCapacityBar/download */
export async function downloadUsingGET3(options?: { [key: string]: any }) {
  return request<any>('/masc/PostDisasterStorageCapacityBar/download', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 查询全部数据 GET /masc/PostDisasterStorageCapacityBar/query */
export async function queryUsingGET3(options?: { [key: string]: any }) {
  return request<Record<string, any>>(
    '/masc/PostDisasterStorageCapacityBar/query',
    {
      method: 'GET',
      ...(options || {}),
    },
  );
}

/** 表格上传 POST /masc/PostDisasterStorageCapacityBar/upload */
export async function uploadUsingPOST19(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/PostDisasterStorageCapacityBar/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
