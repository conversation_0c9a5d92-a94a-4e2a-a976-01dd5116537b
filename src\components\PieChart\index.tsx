import { findAllUsingGET } from '@/services/homepage/shouyejizuleixingjirongliangzhanbi';
import px from '@/utils/px';
import * as echarts from 'echarts';
import React, { useEffect } from 'react';
const UnitType: React.FC = () => {
  useEffect(() => {
    const fetchData = async () => {
      const chartDom = document.getElementById('unitTypeChart');
      const myChart = echarts.init(chartDom!);

      // Get data from API
      const response = await findAllUsingGET();
      const unitData = response.data.map((item) => ({
        name: item.gensetType,
        value: item.installedCapacity,
      }));

      const data = [];
      const color = ['#00ffff', '#00cfff', '#006ced', '#ffe000'];

      for (let i = 0; i < unitData.length; i++) {
        data.push(
          {
            value: unitData[i].value,
            name: unitData[i].name,
            itemStyle: {
              normal: {
                borderWidth: 5,
                shadowBlur: 20,
                borderColor: color[i],
                shadowColor: color[i],
              },
            },
          },
          {
            value: 100,
            name: '',
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                color: 'rgba(0, 0, 0, 0)',
                borderColor: 'rgba(0, 0, 0, 0)',
                borderWidth: 0,
              },
            },
          },
        );
      }

      const option = {
        color: color,
        title: {
          text: '机组类型及占比',
          top: '45%',
          textAlign: 'center',
          left: '25%',
          textStyle: {
            color: '#fff',
            fontSize: px(18),
            fontWeight: '400',
          },
        },
        tooltip: {
          show: true,
          formatter: function (params: any) {
            if (params.name !== '') {
              const total = unitData.reduce((sum, item) => sum + item.value, 0);
              const percent = ((params.value / total) * 100).toFixed(1);
              return `${params.name}<br/>装机容量：${params.value}万千瓦<br/>占比：${percent}%`;
            }
            return '';
          },
        },
        legend: {
          icon: 'circle',
          itemWidth: px(20), // 添加图例标记的宽度
          itemHeight: px(20), // 添加图例标记的高度
          orient: 'vertical',
          formatter: function (name: string) {
            const item = unitData.find((unit) => unit.name === name);
            return item ? `${name}：${item.value}万千瓦` : name;
          },
          right: px(0),
          bottom: px(50),
          align: 'left',
          textStyle: {
            color: '#fff',
            fontSize: px(14),
          },
          itemGap: 20,
          data: unitData.map((item) => item.name),
        },
        series: [
          {
            name: '',
            type: 'pie',
            clockWise: false,
            radius: [px(79), px(84)],
            hoverAnimation: true,
            center: ['25%', '50%'],
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
            },
            data: data,
          },
        ],
      };

      myChart.setOption(option);
    };

    fetchData();

    return () => {
      const chartDom = document.getElementById('unitTypeChart');
      if (chartDom) {
        echarts.getInstanceByDom(chartDom)?.dispose();
      }
    };
  }, []);

  return <div id="unitTypeChart" style={{ width: '100%', height: '100%' }} />;
};

export default UnitType;
