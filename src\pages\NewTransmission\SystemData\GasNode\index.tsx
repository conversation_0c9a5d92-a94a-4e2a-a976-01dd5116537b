import ChartTitle from '@/components/ui/chartTitle';
import _interface from '@/services/interface';
import px from '@/utils/px';
import {
  Button,
  Empty,
  Form,
  Input,
  Popconfirm,
  Space,
  Table,
  Upload,
  message,
} from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.sass';

const { uploadUsingPost5, pageUsingGet3, deleteUsingDelete7, updateUsingPut7 } =
  _interface.tianranqixitongjiedian;

// 添加响应数据类型
interface ResponseData<T> {
  code: number;
  msg: string;
  data: T;
}

const EditableCell = ({
  editing,
  dataIndex,
  title,
  record,
  children,
  ...restProps
}: any) => {
  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[{ required: true, message: `请输入${title}!` }]}
          initialValue={record[dataIndex]}
        >
          <Input />
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const Index = () => {
  const [dataSource, setDataSource] = useState<API.GasNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingKey, setEditingKey] = useState<string>('');

  // Fetch data from backend
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = (await pageUsingGet3({
        current: 1,
        size: 100,
      })) as ResponseData<API.PageGasNode>; // 类型断言

      if (response.code === 1000 && response.data?.records) {
        setDataSource(response.data.records);
      }
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加初始化加载
  useEffect(() => {
    fetchData();
  }, []);

  const customUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      await uploadUsingPost5(formData as any);
      onSuccess('OK');
      // Refresh data after successful upload
      await fetchData();
    } catch (error) {
      onError(error);
      console.error('Upload failed:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加 Form 实例
  const [form] = Form.useForm();

  // 添加删除处理函数
  const handleDelete = async (record: API.GasNode) => {
    try {
      await deleteUsingDelete7({ id: record.id });
      message.success('删除成功');
      fetchData();
    } catch (error) {
      message.error('删除失败');
      console.error('Delete failed:', error);
    }
  };

  // 添加保存处理函数
  const handleSave = async (record: API.GasNode) => {
    try {
      const row = await form.validateFields();
      await updateUsingPut7({ id: record.id }, { ...record, ...row });
      message.success('保存成功');
      setEditingKey('');
      fetchData();
    } catch (error) {
      message.error('保存失败');
      console.error('Save failed:', error);
    }
  };

  const isEditing = (record: any) => record.id === editingKey;

  const handleEdit = (record: API.GasNode) => {
    form.setFieldsValue({
      nodeNo: record.nodeNo,
      nodeLoad: record.nodeLoad,
      minPressure: record.minPressure,
      maxPressure: record.maxPressure,
    });
    setEditingKey(record.id);
  };

  const handleCancel = () => {
    setEditingKey('');
  };

  // 修改列配置
  const columns = [
    {
      title: '节点编号',
      dataIndex: 'nodeNo',
      key: 'nodeNo',
      editable: true,
    },
    {
      title: '负载',
      dataIndex: 'nodeLoad',
      key: 'nodeLoad',
      editable: true,
    },
    {
      title: '最小压力',
      dataIndex: 'minPressure',
      key: 'minPressure',
      editable: true,
    },
    {
      title: '最大压力',
      dataIndex: 'maxPressure',
      key: 'maxPressure',
      editable: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_: any, record: API.GasNode) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button type="link" onClick={() => handleSave(record)}>
              保存
            </Button>
            <Button type="link" onClick={handleCancel}>
              取消
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              type="link"
              disabled={editingKey !== ''}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这条记录吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div className={styles.systemData}>
      <div className={styles.table}>
        <div className={styles.chartTitle}>
          <ChartTitle
            title="节点"
            width={px(600)}
            height={px(37)}
            right={
              <Upload customRequest={customUpload}>
                <Button>上传</Button>
              </Upload>
            }
          />
        </div>
        <div style={{ marginTop: px(20) }}>
          {dataSource.length > 0 ? (
            <Form form={form} component={false}>
              <Table
                components={{
                  body: {
                    cell: EditableCell,
                  },
                }}
                columns={columns.map((col) => {
                  if (!col.editable) {
                    return col;
                  }
                  return {
                    ...col,
                    onCell: (record: API.GasNode) => ({
                      record,
                      dataIndex: col.dataIndex,
                      title: col.title,
                      editing: isEditing(record),
                    }),
                  };
                })}
                dataSource={dataSource}
                loading={loading}
                rowKey="id"
              />
            </Form>
          ) : (
            <Empty description="没有数据，请上传数据" />
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
