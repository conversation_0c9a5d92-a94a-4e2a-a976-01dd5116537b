import LineBarChart from '@/components/chart/LineBarChart';
import NewLine<PERSON>hart from '@/components/NewLineChart';
import ChartTitle from '@/components/ui/chartTitle';
import px from '@/utils/px';
import { Button, Table } from 'antd';

import MultiLineChart from '@/components/MultiLineChart';
import services from '@/services/interface';
import { useRequest } from '@umijs/max';
import { useEffect } from 'react';
import styles from './index.sass';
const { selectOneMonthUsingGet1 } = services.dun;
const { selectAllUsingGet } = services.ranqijizuhaoqixishuchengbenquxian;
const { selectOneDayUsingGet } = services.Hqidianxitongdianjiashuiping24;

const node_columns = [
  {
    title: '节点编号',
    dataIndex: 'nodeId',
    key: 'nodeId',
  },
  {
    title: '气体上限',
    dataIndex: 'upperLimit',
    key: 'upperLimit',
  },
  {
    title: '气体下限',
    dataIndex: 'lowerLimit',
    key: 'lowerLimit',
  },
  {
    title: '气压荷需求',
    dataIndex: 'pressureDemand',
    key: 'pressureDemand',
  },
];

const node_data = [
  { key: '1', nodeId: 67, upperLimit: 96, lowerLimit: 89, pressureDemand: 92 },
  { key: '2', nodeId: 75, upperLimit: 21, lowerLimit: 59, pressureDemand: 51 },
  { key: '3', nodeId: 39, upperLimit: 70, lowerLimit: 66, pressureDemand: 11 },
  { key: '4', nodeId: 90, upperLimit: 63, lowerLimit: 40, pressureDemand: 23 },
  { key: '5', nodeId: 100, upperLimit: 54, lowerLimit: 40, pressureDemand: 8 },
  { key: '6', nodeId: 1, upperLimit: 79, lowerLimit: 93, pressureDemand: 93 },
  { key: '7', nodeId: 65, upperLimit: 35, lowerLimit: 38, pressureDemand: 20 },
  { key: '8', nodeId: 59, upperLimit: 59, lowerLimit: 90, pressureDemand: 86 },
  { key: '9', nodeId: 65, upperLimit: 35, lowerLimit: 38, pressureDemand: 20 },
  { key: '10', nodeId: 59, upperLimit: 59, lowerLimit: 90, pressureDemand: 86 },
  { key: '11', nodeId: 65, upperLimit: 35, lowerLimit: 38, pressureDemand: 20 },
];

const pipe_columns = [
  {
    title: '管道编号',
    dataIndex: 'pipeId',
    key: 'pipeId',
  },
  {
    title: '起始节点',
    dataIndex: 'startNode',
    key: 'startNode',
  },
  {
    title: '终止节点',
    dataIndex: 'endNode',
    key: 'endNode',
  },
  {
    title: '容量',
    dataIndex: 'capacity',
    key: 'capacity',
  },
];
const pipe_data = [
  { key: '1', pipeId: 97, startNode: 57, endNode: 58, capacity: 99 },
  { key: '2', pipeId: 93, startNode: 57, endNode: 34, capacity: 21 },
  { key: '3', pipeId: 93, startNode: 57, endNode: 34, capacity: 21 },
  { key: '4', pipeId: 93, startNode: 57, endNode: 34, capacity: 21 },
  { key: '5', pipeId: 93, startNode: 57, endNode: 34, capacity: 21 },
  { key: '6', pipeId: 93, startNode: 57, endNode: 34, capacity: 21 },
  { key: '7', pipeId: 17, startNode: 72, endNode: 90, capacity: 13 },
  { key: '8', pipeId: 1, startNode: 75, endNode: 13, capacity: 59 },
  { key: '9', pipeId: 48, startNode: 79, endNode: 83, capacity: 43 },
  { key: '10', pipeId: 20, startNode: 85, endNode: 15, capacity: 56 },
  { key: '11', pipeId: 17, startNode: 87, endNode: 46, capacity: 87 },
  { key: '12', pipeId: 43, startNode: 100, endNode: 14, capacity: 93 },
];
const xAxis = [
  '00:00',
  '01:00',
  '02:00',
  '03:00',
  '04:00',
  '05:00',
  '06:00',
  '07:00',
  '08:00',
  '09:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',
  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
];
const dates = [
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23',
  '24',
  '25',
  '26',
  '27',
  '28',
  '29',
  '30',
];
const coal_prices = [
  1000, 1075, 1275, 1210, 1315, 1405, 1580, 1625, 1680, 1700, 1750, 1810, 1775,
  1940, 1975, 2025, 2100, 2110, 2130, 2250, 2010, 1955, 1870, 1810, 1600, 1545,
  1450, 1410, 1375, 1160,
];
const data = [
  42.87, 12.55, 74.39, 28.04, 90.22, 34.11, 56.89, 19.34, 81.55, 62.45, 7.12,
  47.88, 23.99, 54.32, 11.01, 65.88, 37.44, 88.11, 29.55, 44.22, 51.33, 77.77,
  38.89, 15.55,
];
const gasAmount = [
  0, 26, 53, 79, 105, 132, 158, 184, 211, 237, 263, 290, 316, 342, 369, 395,
  421, 448, 474, 500,
];
const y_values_curve1 = [
  120.0, 204.4176, 306.865, 427.3444, 565.855, 722.3968, 896.9696, 1089.5736,
  1299.2089, 1525.8756, 1769.5735, 2029.2024, 2304.7624, 2595.2536, 2899.6761,
  3217.0296, 3546.3145, 3886.5204, 4236.6473, 4596.696,
];
const y_values_curve2 = [
  78.0, 200.932, 341.882, 500.852, 677.842, 872.852, 1085.882, 1316.932,
  1565.902, 1832.8, 2117.622, 2420.362, 2741.022, 3079.602, 3436.102, 3810.522,
  4202.862, 4613.122, 5041.202, 5487.1,
];
const y_values_curve3 = [
  260.0, 374.255, 506.635, 657.045, 825.485, 1011.955, 1216.455, 1438.985,
  1679.545, 1938.135, 2214.755, 2509.305, 2821.785, 3152.195, 3499.535,
  3863.805, 4244.995, 4643.105, 5057.135, 5486.085,
];
const y_values_curve4 = [
  400.0, 532.432, 682.9, 851.392, 1037.808, 1242.144, 1463.408, 1701.592,
  1956.7, 2228.732, 2517.688, 2823.564, 3146.364, 3485.088, 3840.732, 4212.296,
  4599.784, 4902.192, 5219.52, 5551.76,
];

export const SystemData = () => {
  return (
    <div className={styles.systemData}>
      <div className={styles.table}>
        <div className={styles.chartTitle}>
          <ChartTitle
            title="节点"
            width={px(600)}
            height={px(37)}
            right={<Button>上传</Button>}
          />
        </div>
        <div style={{ marginTop: px(20) }}>
          <Table columns={node_columns} dataSource={node_data} />
        </div>
      </div>
      <div className={styles.table}>
        <div className={styles.chartTitle}>
          <ChartTitle
            title="管道"
            width={px(600)}
            height={px(37)}
            right={<Button>上传</Button>}
          />
        </div>
        <div style={{ marginTop: px(20) }}>
          <Table columns={pipe_columns} dataSource={pipe_data} />
        </div>
      </div>
    </div>
  );
};
const numericArray3 = [
  [96, 56],
  [4, 27],
  [35, 58],
  [39, 43],
  [44, 14],
  [86, 49],
  [64, 92],
  [45, 82],
];
const rowData3 = numericArray3.map((row) => ({
  time1: row[0],
  price1: row[1],
}));
const gasPriceDef = [
  { headerName: '时间点', field: 'time1' },
  { headerName: '价格', field: 'price1' },
];
const loadDef = [
  { headerName: '时间点', field: 'time1' },
  { headerName: '负荷', field: 'price1' },
];
const category = [
  '2022-01-01',
  '2022-01-02',
  '2022-01-03',
  '2022-01-04',
  '2022-01-05',
  '2022-01-06',
  '2022-01-07',
];

const lineData = [120, 132, 101, 134, 90, 230, 210];

const barData = [80, 90, 70, 60, 10, 100, 80];

export const ItemData = () => {
  return (
    <div className={styles.runData}>
      <div className={styles.table}>
        <div className={styles.chartTitle}>
          <ChartTitle
            title="价格曲线"
            width={px(600)}
            height={px(37)}
            right={<Button>上传</Button>}
          />
        </div>
        <div style={{ marginTop: px(20) }} className={styles.content}>
          <div className={styles.chart}>
            <LineBarChart
              category={category}
              lineData={lineData}
              barData={barData}
              xlabelName="时间"
              ylabelName="价格"
            />
          </div>
          <div className={styles.chart}>
            <Table
              columns={node_columns}
              dataSource={node_data}
              pagination={{
                pageSize: 6,
              }}
            />
          </div>
        </div>
      </div>
      <div className={styles.table}>
        <div className={styles.chartTitle}>
          <ChartTitle
            title="负荷曲线"
            width={px(600)}
            height={px(37)}
            right={<Button>上传</Button>}
          />
        </div>
        <div style={{ marginTop: px(20) }} className={styles.content}>
          <div className={styles.chart}>
            <LineBarChart
              category={category}
              lineData={lineData}
              barData={barData}
              xlabelName="时间"
              ylabelName="价格"
            />
          </div>
          <div className={styles.chart}>
            <Table
              columns={node_columns}
              dataSource={node_data}
              pagination={{
                pageSize: 6,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export const RunData = () => {
  const { run: runCoal, data: coalPrice } = useRequest(
    selectOneMonthUsingGet1,
    {
      manual: true,
    },
  );
  const { run: runParameter, data: parameters } = useRequest(
    selectAllUsingGet,
    {
      manual: true,
    },
  );

  useEffect(() => {
    runCoal();
    runParameter();
  }, []);
  return (
    <div className={styles.runContainer}>
      <div className={styles.chartTitle}>30日市场动力煤价格曲线</div>
      <div className={styles.newchart}>
        <NewLineChart
          legend="30日市场动力煤价格曲线（元/吨）"
          seriesData={coalPrice}
          xAxisData={dates}
          xUnit="日"
          yUnit="元/吨"
        />
      </div>
      <div className={styles.chartTitle}>燃气机组耗气系数成本曲线</div>
      <div className={styles.newchart}>
        {parameters && (
          <MultiLineChart seriesData={parameters.y} xAxisData={parameters.x} />
        )}
      </div>
    </div>
  );
};

export const Result = () => {
  const { run: runResult, data: result } = useRequest(selectOneDayUsingGet, {
    manual: true,
  });

  useEffect(() => {
    runResult();
  }, []);

  return (
    <div className={styles.runContainer}>
      <div className={styles.newchart}>
        <NewLineChart
          legend="24h系统电价水平"
          seriesData={result}
          xAxisData={xAxis}
          xUnit="时"
          yUnit="元/KWh"
        />
      </div>
    </div>
  );
};
