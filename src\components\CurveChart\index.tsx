import px from '@/utils/px';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';

interface CurveChartProps {
  data: number[];
  yAxisName?: string;
  legendName?: string;
  color?: string;
  height?: number;
}

const CurveChart: React.FC<CurveChartProps> = ({
  data,
  yAxisName = ' ',
  legendName = ' ',
  color = '#6affeb',
  height = px(240),
}) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const myChart = echarts.init(chartRef.current);

    const hexToRgb = (hexColor: string) => {
      const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
      const processedHex = hexColor.replace(
        shorthandRegex,
        (m, r, g, b) => r + r + g + g + b + b,
      );

      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(
        processedHex,
      );
      return result
        ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16),
          }
        : null;
    };

    // X轴时间数据
    const xData = Array.from({ length: data.length }, (_, i) => i + 1);

    const option = {
      legend: {
        show: true,
        data: [legendName],
        right: '10%',
        top: '0%',
        textStyle: {
          fontSize: px(14),
          color: 'white',
        },
        itemStyle: {
          borderColor: 'transparent',
        },
      },
      series: [
        {
          name: legendName,
          data: data,
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbolSize: 10,
          emphasis: { focus: 'series' },
          animationDuration: 2500,
          animationEasing: 'cubicInOut',
          lineStyle: {
            width: 2,
            color: color,
          },
          areaStyle: {
            width: 4,
            opacity: 0.25,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0.389, color: color },
                {
                  offset: 1,
                  color: `rgba(${hexToRgb(color)?.r},${hexToRgb(color)?.g},${
                    hexToRgb(color)?.b
                  },0)`,
                },
              ],
              global: false,
            },
          },
          itemStyle: {
            color: color,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
        },
        textStyle: {
          color: '#fafafa',
        },
        borderColor: 'transparent',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        extraCssText: 'backdrop-filter: blur(6px);',
      },
      grid: {
        top: '25%',
        left: '3%',
        right: '2.5%',
        bottom: '1%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xData,
        axisLine: { show: false },
        axisTick: { show: false },
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLabel: {
          color: 'white',
          fontSize: px(14),
          formatter: '{value}时',
        },
      },
      yAxis: {
        name: yAxisName,
        type: 'value',
        nameLocation: 'end',
        nameTextStyle: {
          color: 'white',
          fontSize: px(14),
        },
        axisLabel: {
          color: 'white',
          fontSize: px(14),
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#062b45',
            width: 1,
            type: 'dashed',
          },
        },
      },
    };

    myChart.setOption(option);

    return () => {
      myChart.dispose();
    };
  }, [data, yAxisName, legendName, color]);

  return <div ref={chartRef} style={{ width: '100%', height }} />;
};

export default CurveChart;
