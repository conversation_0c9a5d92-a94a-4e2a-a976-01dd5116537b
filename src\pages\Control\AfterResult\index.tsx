import CustomPane from '@/components/CustomPane';
import { queryUsingGET1 } from '@/services/result/DisasterFlexibleLoadPlanController';
import { queryUsingGET3 } from '@/services/result/DisasterStorageCapacityBarController';
import { queryUsingGET4 } from '@/services/result/DisasterStorageCurveController';
import px from '@/utils/px';
import { useRequest } from '@umijs/max';
import { useEffect } from 'react';
import BarChart from './BarChart';
import LineChart from './LineChart';
import styles from './page.module.sass';

const Index = () => {
  const { run: runStorage, data: storageCurveData } = useRequest(
    () => queryUsingGET4(),
    { manual: true },
  );
  const { run: runFlexibleLoad, data: flexibleLoadCurveData } = useRequest(
    () => queryUsingGET1(),
    { manual: true },
  );
  const { run: runAdjustableCapacity, data: adjustableCapacityData } =
    useRequest(() => queryUsingGET3(), { manual: true });

  // 储能运行计划数据
  const storageData = storageCurveData
    ? {
        xAxisData: storageCurveData.hours.map(String),
        yAxisData: storageCurveData.values,
        xAxisName: '时间',
        yAxisName: '功率/万千瓦',
        legendName: '储能类资源运行计划',
        color: '#6affeb',
        height: px(400),
      }
    : undefined;

  // 灵活负荷运行计划数据
  const flexibleLoadData = flexibleLoadCurveData
    ? {
        xAxisData: flexibleLoadCurveData.hours.map(String),
        yAxisData: flexibleLoadCurveData.values,
        xAxisName: '时间',
        yAxisName: '功率/万千瓦',
        legendName: '灵活负荷运行计划',
        color: '#6affeb',
        height: px(400),
      }
    : undefined;
  useEffect(() => {
    runStorage();
    runFlexibleLoad();
    runAdjustableCapacity();
    console.log(storageData, flexibleLoadData, adjustableCapacityData);
  }, []);
  return (
    <div className={styles.container}>
      <div className={styles.row}>
        <div className={styles.lineChart}>
          <CustomPane title="储能类资源运行折线图">
            {storageData && <LineChart {...storageData} />}
          </CustomPane>
        </div>
        <div className={styles.lineChart}>
          <CustomPane title="灵活负荷运行计划">
            {flexibleLoadData && <LineChart {...flexibleLoadData} />}
          </CustomPane>
        </div>
      </div>
      <div className={styles.row}>
        <CustomPane title="储能类资源可调容量柱状图">
          <BarChart
            containerId="energyStorageChart"
            xData={adjustableCapacityData?.hours}
            yData={adjustableCapacityData?.values}
          />
        </CustomPane>
      </div>
    </div>
  );
};

export default Index;
