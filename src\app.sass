@import "~@/assets/css/helper.sass"


:global
    body, html, #root, .ant-pro-layout
        overflow: hidden
        height: 100%
        margin: 0
        padding: 0

.layout
    display: flex
    flex-direction: row
    align-items: center
    width: px(1920)
    height: px(99)
    color: white
    background: url('./assets/images/headerbg4.png')
    background-size:100% 100%
    background-repeat: no-repeat 
    .title
        background-clip: text
        text-fill-color: transparent
        font-family: youshe
        font-size: px(40)
        font-weight: 400
        padding-left: px(120)
        letter-spacing: px(5)
        text-align: center
    .menu
        flex-grow: 1
        display: flex
        justify-content: flex-end
        color: rgba(197, 208, 212, 0.45)
        font-size: px(24)
        margin-top: px(20)
        margin-right: px(30)
        &Item
            font-family: youshe
            font-size: px(26)
            align-items: center
            text-align: center
            padding: px(10) 
            height: px(90)
            width: px(350)
            background: url('./assets/images/navigation_bg.png')
            background-size:100% 100%
            background-repeat: no-repeat 
            &:hover
                color: white
                cursor: pointer
        &ItemActive
            color: white
            

.outClass
    overflow: hidden
    :global
        .ant-pro-layout
            width: 100vw
        .ant-pro-layout .ant-pro-layout-bg-list,.ant-pro-layout .ant-layout-header.ant-pro-layout-header
            backdrop-filter: blur(0)
        .ant-pro-layout .ant-pro-layout-content
            padding-block: px(0)
            padding-inline: px(0)
            height: 100%
        .ant-pro-layout .ant-layout-header.ant-pro-layout-header-fixed-header
            
        .ant-pro-layout .ant-layout-header.ant-pro-layout-header
            height: px(100)
            border: px(0)
