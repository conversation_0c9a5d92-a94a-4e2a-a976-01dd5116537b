// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/GasPipe/delete */
export async function deleteUsingDelete8(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE8Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasPipe/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/GasPipe/page */
export async function pageUsingGet4(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGET4Params,
  options?: { [key: string]: any },
) {
  return request<API.PageGasPipe>('/masc/GasPipe/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/GasPipe/save */
export async function saveUsingPost8(
  body: API.GasPipe,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasPipe/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/GasPipe/update */
export async function updateUsingPut8(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT8Params,
  body: API.GasPipe,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasPipe/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/GasPipe/upload */
export async function uploadUsingPost6(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasPipe/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
