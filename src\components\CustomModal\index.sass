@import "~@/assets/css/helper.sass"

.box
    border-radius: px(20)
    :global
        .ant-modal-content,
        .ant-modal-header
            background: linear-gradient(180deg, #0f213f 0%, #01122f 100%)
            color: white
            border: 1px solid rgba(255, 255, 255, 0.1)
            box-shadow: 0 px(8) px(32) rgba(0, 0, 0, 0.3)

        .ant-modal-header
            border-bottom: 1px solid rgba(255, 255, 255, 0.1)
            padding: px(20) px(24)
            margin: 0

        .ant-modal-body
            padding: px(30)
            min-height: px(120)
            display: flex
            flex-direction: column
            justify-content: center
            align-items: center
            font-size: px(16)
            line-height: 1.8

        .ant-upload-list-item-name,
        .ant-btn,
        .ant-upload-list-item-action,
        .ant-upload-list-item-actions
            color: white

        .ant-btn-default
            color: black

        .ant-upload-drag-container
            p.ant-upload-text,
            p.ant-upload-hint
                color: white

        .ant-modal-title
            color: white
            font-size: px(20)
            text-align: center
            margin: 0
            font-weight: 500

        .ant-modal-close
            position: absolute
            top: px(16)
            right: px(16)
            width: px(32)
            height: px(32)
            display: flex
            align-items: center
            justify-content: center
            border-radius: 50%
            transition: all 0.3s
            
            &:hover
                background: rgba(255, 255, 255, 0.1)
            
            .ant-modal-close-x
                color: rgba(255, 255, 255, 0.8)
                font-size: px(16)
                width: px(32)
                height: px(32)
                line-height: px(32)
                display: flex
                align-items: center
                justify-content: center
                margin: 0
                
                &:hover
                    color: white
