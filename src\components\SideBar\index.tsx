import React from 'react';
import styles from './index.sass';

type SidebarProps = {
  menu: Array<{ key: string; label: string }>;
  activeId: string;
  onChange: (key: string) => void;
};

const Sidebar: React.FC<SidebarProps> = ({ menu, activeId, onChange }) => {
  const handleClick = (key: string) => {
    onChange(key);
  };

  return (
    <div className={styles.sidebarContainer}>
      <div className={styles.sidebar}>
        <ul>
          {menu.map((item) => (
            <li
              key={item.key}
              className={item.key === activeId ? styles.active : ''}
              onClick={() => handleClick(item.key)}
            >
              {item.label}
            </li>
          ))}
        </ul>
      </div>
      <div className={styles.divider}></div>
    </div>
  );
};

export default Sidebar;
