@import "~@/assets/css/helper.sass"
 
.powerLoadForecast
  --primary-dark: #0a1525
  --secondary-dark: rgba(255, 255, 255, 0)
  --accent-blue: #47c3fd
  --accent-green: #47c3fd
  --accent-cyan: #f8f8f8
  --text-primary: rgba(255, 255, 255, 0.9)
  --text-secondary: rgba(255, 255, 255, 0.6)
  --card-bg: rgba(255, 255, 255, 0.05)
  --card-border: rgba(0, 174, 255, 0.2)
  --hover-bg: rgba(255, 255, 255, 0.1)
  --active-bg: rgba(0, 174, 255, 0.2)

  width: px(1920)
  height: px(1080)
 
  color: var(--text-primary)
  overflow: hidden
  position: relative
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif
  font-size: px(24)

  *
    margin: 0
    padding: 0
    box-sizing: border-box

  .gridOverlay
    position: absolute
    top: 0
    left: 0
    width: 100%
    height: 100%
    background: radial-gradient(circle at 10% 20%, rgba(0, 174, 255, 0.1) 0%, transparent 20%), radial-gradient(circle at 90% 80%, rgba(106, 255, 235, 0.1) 0%, transparent 20%)
    pointer-events: none
    z-index: 0

  .glow
    position: absolute
    width: px(300)
    height: px(300)
    border-radius: 50%
    background: radial-gradient(circle, rgba(106, 183, 255, 0.2) 0%, transparent 70%)
    filter: blur(px(30))
    z-index: 1

    &.glow1
      top: px(-100)
      left: px(-100)

    &.glow2
      bottom: px(-100)
      right: px(-100)
      background: radial-gradient(circle, rgba(0, 174, 255, 0.2) 0%, transparent 70%)

  .container
    display: grid
    grid-template-columns: px(340) 1fr
    gap: px(15)
    width: 100%
    height: 100%
    padding: px(15)
    position: relative
    z-index: 10

  .sidebar
    background: rgba(15, 59, 67, 0.333) 
    border-radius: px(16)
    padding: px(15)
    box-shadow: 0 px(2) px(10) rgba(255, 255, 255, 0.333)
    border: px(2) solid rgba(32, 166, 255, 0.323) 
   
    display: flex
    flex-direction: column
    gap: px(15)
    height: px(900)
    overflow-y: auto
   
    font-size: px(22)

    &::-webkit-scrollbar
      width: px(6)

    &::-webkit-scrollbar-track
      background: rgba(255, 255, 255, 0)
      border-radius: px(3)

    &::-webkit-scrollbar-thumb
      background: var(--accent-blue)
      border-radius: px(3)

  .tabs
    display: flex
    background: rgba(0, 0, 0, 0.3)
    border-radius: px(12)
    padding: px(4)
    border: px(2) solid var(--card-border)

    .tab
      flex: 1
      text-align: center
      padding: px(10) px(5)
      border-radius: px(8)
      cursor: pointer
      transition: all 0.3s ease
      font-weight: 500
      font-size: px(20)
      color: var(--text-secondary)

      &.active
        background: var(--active-bg)
        color: var(--accent-green)
        box-shadow: 0 0 px(15) rgba(106, 208, 255, 0.2)

      &:hover:not(.active)
        background: var(--hover-bg)

  .section
    background: rgba(167, 167, 167, 0)
    border-radius: px(12)
    padding: px(15)
    border: px(1) solid var(--card-border)

    .sectionTitle
      display: flex
      align-items: center
      gap: px(8)
      font-size: px(18)
      margin-bottom: px(12)
      color: var(--accent-cyan)

      i
        font-size: px(16)

  .uploadArea
    background: rgba(255, 255, 255, 0.118)
    border: px(2) dashed var(--card-border)
    border-radius: px(10)
    padding: px(20) px(10)
    text-align: center
    cursor: pointer
    transition: all 0.3s ease
    margin-bottom: px(12)

    &:hover
      border-color: var(--accent-blue)
      background: rgba(0, 174, 255, 0.1)

    .uploadIcon
      font-size: px(32)
      margin-bottom: px(8)
      color: var(--accent-blue)

    .uploadText
      font-size: px(20)
      color: var(--text-primary)
      margin-bottom: px(4)

    .uploadSubtext
      font-size: px(12)
      color: var(--text-secondary)

  .inputGroup
    margin-bottom: px(15)
    font-size: px(20)

    label
      display: block
      margin-bottom: px(6)
      font-size: px(18)
      color: var(--text-secondary)

    input, select
      width: 100%
      padding: px(10) px(12)
      background: rgba(0, 0, 0, 0.3)
      border: 1px solid var(--card-border)
      border-radius: px(8)
      color: var(--text-primary)
      font-size: px(16)
      outline: none
      transition: all 0.3s ease

      &:focus
        border-color: var(--accent-blue)
        box-shadow: 0 0 0 px(2) rgba(0, 174, 255, 0.2)

      &::placeholder
        color: rgba(255, 255, 255, 0.4)

  .inputWrapper
    position: relative

    .units
      position: absolute
      right: px(12)
      top: 50%
      transform: translateY(-50%)
      color: var(--text-secondary)
      font-size: px(13)

  .mainContent
    display: flex
    flex-direction: column
    gap: px(15)
    height: 100%
    overflow-y: auto
    font-size: px(24)

    &::-webkit-scrollbar
      width: px(6)

    &::-webkit-scrollbar-track
      background: rgba(255, 255, 255, 0.05)
      border-radius: px(3)

    &::-webkit-scrollbar-thumb
      background: var(--accent-blue)
      border-radius: px(3)

  .chartContainer
    background: rgba(255, 255, 255, 0.071)
    border-radius: px(16)
    padding: px(20)
    box-shadow: 0 px(10) px(30) rgba(0, 0, 0, 0.3)
    border: 1px solid var(--card-border)
    height: px(380)
    position: relative
    overflow: hidden

    .chartTitle
      font-size: px(17)
      margin-bottom: px(15)
      color: var(--accent-green)
      display: flex
      align-items: center
      gap: px(10)

      i
        font-size: px(18)

    .chart
      width: 100%
      height: calc(100% - px(20))

  .statsContainer
    display: grid
    grid-template-columns: repeat(3, 1fr)
    gap: px(15)
    margin-top: px(5)
    font-size: px(24)

    .statCard
      background: rgba(1, 116, 161, 0.527)
      border-radius: px(14)
      padding: px(18)
      text-align: center
      transition: all 0.3s ease

      &:hover
        transform: translateY(px(-5))
        box-shadow: 0 px(10) px(25) rgba(0, 174, 255, 0.2)
        border-color: var(--accent-blue)

      .statTitle
        font-size: px(24)
        color: rgb(255, 255, 255)
        margin-bottom: px(10)

      .statValue
        font-size: px(30)
        font-weight: 700
        background: linear-gradient(90deg, #1ebbff, rgba(0, 255, 255, 1))
        -webkit-background-clip: text
        -webkit-text-fill-color: transparent
        background-clip: text