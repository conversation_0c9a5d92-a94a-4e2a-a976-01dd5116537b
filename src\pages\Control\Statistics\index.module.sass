@import "~@/assets/css/helper.sass"

// 变量定义
$primaryDark: #0a1525
$secondaryDark: rgba(76, 76, 76, 0.153)
$accentBlue: #00aeff
$accentGreen: #6affeb
$accentCyan: #00f7ff
$textPrimary: rgba(255, 255, 255, 0.9)
$textSecondary: rgba(255, 255, 255, 0.6)
$cardBg: rgba(255, 255, 255, 0.05)
$cardBorder: rgba(0, 174, 255, 0.2)
$hoverBg: rgba(255, 255, 255, 0.1)
$activeBg: rgba(0, 174, 255, 0.2)

// 全局重置
*
  margin: 0
  padding: 0
  box-sizing: border-box
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif
  font-size: px(18)

.electrolyticAluminumCalculator
  color: $textPrimary
  min-height: 100vh
  overflow-x: hidden
  position: relative

// 网格背景
.gridOverlay
  position: fixed
  top: 0
  left: 0
  width: 100%
  height: 100%
  pointer-events: none
  z-index: 0

// 主容器
.container
  display: grid
  grid-template-columns: px(400) 1fr px(350)
  gap: px(20)
  max-width: px(1800)
  margin: 0 auto
  padding: px(20)
  position: relative
  z-index: 10
  height: px(930)

// 左侧输入面板
.inputPanel
  width: px(400)
  border-radius: px(16)
  padding: px(25)
  box-shadow: 0 px(10) px(30) rgba(0, 0, 0, 0.3)
  border: px(1) solid $cardBorder
  backdrop-filter: blur(px(10))
  display: flex
  align-items: center
  flex-direction: column
  gap: px(20)
  overflow-y: auto

// 输入区域
.inputSection
  color: white
  margin-top: px(30)
  background: $cardBg
  border-radius: px(12)
  padding: px(20)
  border: px(1) solid $cardBorder

.inputGrid
  gap: px(40)

.inputGroup
  font-size: px(30)
  margin-bottom: px(40)

  label
    display: block
    margin-bottom: px(10)
    font-size: px(18)
    color: $textSecondary

.inputWrapper
  position: relative

.inputGroup input
  width: 100%
  padding: px(14) px(20)
  background: rgba(0, 0, 0, 0.3)
  border: px(1) solid $cardBorder
  border-radius: px(10)
  color: $textPrimary
  font-size: px(18)
  outline: none
  transition: all 0.3s ease

  &:focus
    border-color: $accentBlue
    box-shadow: 0 0 0 3px rgba(0, 174, 255, 0.2)

// 计算按钮区域
.calculateSection
  margin-top: px(30)
  display: flex
  justify-content: center

.calculateButton
  padding: px(15) px(40)
  background: linear-gradient(135deg, $accentBlue, $accentCyan)
  border: none
  border-radius: px(12)
  color: white
  font-size: px(18)
  font-weight: 600
  cursor: pointer
  transition: all 0.3s ease
  box-shadow: 0 px(8) px(25) rgba(0, 174, 255, 0.3)

  &:hover
    transform: translateY(px(-2))
    box-shadow: 0 px(12) px(35) rgba(0, 174, 255, 0.4)

  &:disabled
    opacity: 0.6
    cursor: not-allowed

  &.pulse
    animation: pulse 1s infinite

@keyframes pulse
  0%
    box-shadow: 0 0 0 0 rgba(0, 174, 255, 0.7)
  70%
    box-shadow: 0 0 0 px(10) rgba(0, 174, 255, 0)
  100%
    box-shadow: 0 0 0 0 rgba(0, 174, 255, 0)

// 中间评估指标面板
.middlePanel
  display: flex
  flex-direction: column
  border-radius: px(16)
  padding: px(25)
  box-shadow: 0 px(10) px(30) rgba(0, 0, 0, 0.3)
  border: px(1) solid $cardBorder
  backdrop-filter: blur(px(10))

.indicatorsGrid
  display: flex
  flex-direction: column
  gap: px(40)
  margin-top: px(20)
  height: 100%
  justify-content: space-between

.indicatorItem
  background: $cardBg
  border-radius: px(12)
  padding: px(30) px(25)
  border: px(1) solid $cardBorder
  text-align: center
  flex: 1
  display: flex
  flex-direction: column
  justify-content: center
  align-items: center

.indicatorLabel
  font-size: px(18)
  color: $textSecondary
  margin-bottom: px(20)

.indicatorValue
  font-size: px(42)
  font-weight: 600
  color: $accentGreen
  display: flex
  align-items: baseline
  justify-content: center
  gap: px(10)

.unit
  font-size: px(22)
  color: $textSecondary

// 右侧图表面板
.chartPanel
  display: flex
  flex-direction: column
  gap: px(20)
  width: px(700)
  height: 100%
  overflow-y: auto

.chartContainer
  height: px(420)
  padding: px(20)
 
  border-radius: px(16)
  box-shadow: 0 px(10) px(30) rgba(0, 0, 0, 0.3)
  border: px(1) solid $cardBorder
  backdrop-filter: blur(px(10))

  :global
    .box
      background-color: rgba(255, 255, 255, 0.05)

// 发光效果
.glow
  position: fixed
  border-radius: 50%
  filter: blur(px(100))
  pointer-events: none
  z-index: 1

.glow1
  width: px(400)
  height: px(400)
  background: radial-gradient(circle, rgba(0, 174, 255, 0.15) 0%, transparent 70%)
  top: 20%
  left: 10%
  animation: float 6s ease-in-out infinite

.glow2
  width: px(300)
  height: px(300)
  background: radial-gradient(circle, rgba(106, 255, 235, 0.1) 0%, transparent 70%)
  bottom: 20%
  right: 15%
  animation: float 8s ease-in-out infinite reverse

@keyframes float
  0%, 100%
    transform: translateY(0px)
  50%
    transform: translateY(px(-20))
