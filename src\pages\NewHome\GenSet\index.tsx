import CustomButton from '@/components/CustomButton';
import CustomModal from '@/components/CustomModal';
import CustomTable from '@/components/CustomTable';
import {
  deleteUsingDELETE3,
  pageUsingGET1,
  saveUsingPOST3,
  updateUsingPUT3,
  uploadUsingPOST3,
} from '@/services/homepage/shouyefenbushixiaoxingranmeiranqijizu';
import px from '@/utils/px';
import { CloudUploadOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Space,
  Upload,
} from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.sass';

interface Props {
  name?: string;
  infoOpen: boolean;
  onCancelInfo: () => void;
}

interface TableRecord {
  genNumber: number;
  id: number;
  [key: string]: any; // for hour1-hour24
}

const Index: React.FC<Props> = (props: Props) => {
  const { infoOpen, onCancelInfo } = props;
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<TableRecord | null>(null);
  const [tableData, setTableData] = useState<TableRecord[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();

  const fetchTableData = async (page: number = 1) => {
    try {
      const response = await pageUsingGET1({
        current: page,
        size: 7,
      });
      if (response.code === 1000) {
        setTableData(response.data.records);
        setTotalRecords(response.data.total);
      }
    } catch (error) {
      message.error('获取数据失败');
    }
  };

  useEffect(() => {
    if (infoOpen) {
      fetchTableData();
    }
  }, [infoOpen]);

  const handleEdit = (record: TableRecord) => {
    setCurrentRecord(record);
    // Transform record to match form fields
    const formValues = {
      unitNumber: record.genNumber,
      ...Object.fromEntries(
        Array.from({ length: 24 }, (_, i) => [
          `t${i + 1}`,
          record[`hour${i + 1}`],
        ]),
      ),
    };
    form.setFieldsValue(formValues);
    setEditModalVisible(true);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      // Transform form values back to API format
      const apiValues = {
        id: currentRecord?.id,
        genNumber: values.unitNumber,
        ...Object.fromEntries(
          Array.from({ length: 24 }, (_, i) => [
            `hour${i + 1}`,
            values[`t${i + 1}`],
          ]),
        ),
      };

      // Call the update API
      const response = await updateUsingPUT3(
        { id: currentRecord?.id }, // params
        apiValues, // body
      );

      if (response.code === 1000) {
        message.success('保存成功');
        setEditModalVisible(false);
        // Refresh table data after successful update
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('Save failed:', error);
      message.error('保存失败');
    }
  };

  const handleDelete = async (record: TableRecord) => {
    try {
      const response = await deleteUsingDELETE3({
        id: record.id,
      });

      if (response.code === 1000) {
        message.success('删除成功');
        // Refresh table data after successful deletion
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('Delete failed:', error);
      message.error('删除失败');
    }
  };

  const handleAdd = () => {
    addForm.resetFields();
    setAddModalVisible(true);
  };

  const handleAddSave = async () => {
    try {
      const values = await addForm.validateFields();
      // Transform form values to match API format
      const apiValues = {
        genNumber: Number(values.unitNumber),
        hour1: Number(values.t1),
        hour2: Number(values.t2),
        hour3: Number(values.t3),
        hour4: Number(values.t4),
        hour5: Number(values.t5),
        hour6: Number(values.t6),
        hour7: Number(values.t7),
        hour8: Number(values.t8),
        hour9: Number(values.t9),
        hour10: Number(values.t10),
        hour11: Number(values.t11),
        hour12: Number(values.t12),
        hour13: Number(values.t13),
        hour14: Number(values.t14),
        hour15: Number(values.t15),
        hour16: Number(values.t16),
        hour17: Number(values.t17),
        hour18: Number(values.t18),
        hour19: Number(values.t19),
        hour20: Number(values.t20),
        hour21: Number(values.t21),
        hour22: Number(values.t22),
        hour23: Number(values.t23),
        hour24: Number(values.t24),
      };

      // Call the create API
      const response = await saveUsingPOST3(apiValues);

      if (response.code === 1000) {
        message.success('添加成功');
        setAddModalVisible(false);
        // Refresh table data after successful creation
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '添加失败');
      }
    } catch (error) {
      console.error('Add failed:', error);
      message.error('添加失败');
    }
  };

  const handleUpload = async (file: RcFile) => {
    try {
      // 创建 FormData 对象
      const formData = new FormData();
      formData.append('file', file);

      // 调用上传 API
      const response = await uploadUsingPOST3(formData);

      if (response.code === 1000) {
        message.success('上传成功');
        // 刷新表格数据
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '上传失败');
      }

      // 返回 false 阻止 Upload 组件的默认上传行为
      return false;
    } catch (error) {
      console.error('Upload failed:', error);
      message.error('上传失败');
      return false;
    }
  };

  const columns = [
    {
      title: '机组编号',
      dataIndex: 'genNumber',
      key: 'genNumber',
      width: 80,
      fixed: 'left',
    },
    ...Array.from({ length: 24 }, (_, i) => ({
      title: String(i + 1),
      dataIndex: `hour${i + 1}`,
      key: `hour${i + 1}`,
    })),
    {
      title: '操作',
      key: 'operation',
      fixed: 'right',
      width: 120,
      render: (_: any, record: TableRecord) => (
        <Space>
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const formItems = (
    <Row gutter={8}>
      <Col span={4}>
        <Form.Item
          label="机组编号"
          name="unitNumber"
          rules={[{ required: true, message: '请输入机组编号' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      {Array.from({ length: 24 }, (_, i) => i + 1).map((hour) => (
        <Col span={4} key={hour}>
          <Form.Item
            label={`${hour}时`}
            name={`t${hour}`}
            rules={[{ required: true, message: `请输入${hour}时数据` }]}
          >
            <Input />
          </Form.Item>
        </Col>
      ))}
    </Row>
  );

  return (
    <div className={styles.container}>
      <CustomModal
        open={infoOpen}
        title="分布式小型燃煤燃气机组"
        onCancel={onCancelInfo}
        footer={null}
        width={'90vw'}
        className={styles.customModal}
        centered={true}
      >
        <div className={styles.box}>
          <div style={{ display: 'flex' }}>
            <CustomButton icon={<PlusOutlined />} onClick={handleAdd}>
              添加
            </CustomButton>
            <Upload beforeUpload={handleUpload} showUploadList={false}>
              <CustomButton icon={<CloudUploadOutlined />}>导入</CustomButton>
            </Upload>
          </div>
          <CustomTable
            className={styles.customTable}
            columns={columns}
            dataSource={tableData}
            scroll={{ x: 'max-content' }}
            pagination={{
              current: currentPage,
              pageSize: 7,
              total: totalRecords,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              onChange: (page) => {
                setCurrentPage(page);
                fetchTableData(page);
              },
            }}
          />
        </div>
      </CustomModal>

      {/* 编辑弹窗 */}
      <Modal
        title="编辑节点"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={handleSave}
        width={px(1200)}
      >
        <Form form={form} layout="vertical" initialValues={currentRecord}>
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>

      {/* 添加弹窗 */}
      <Modal
        title="添加节点"
        open={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onOk={handleAddSave}
        width={px(1200)}
      >
        <Form form={addForm} layout="vertical">
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>
    </div>
  );
};

export default Index;
