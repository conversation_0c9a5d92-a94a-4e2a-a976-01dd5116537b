@import "~@/assets/css/helper.sass"

.sidebarContainer
  display: flex
  flex-direction: row
  align-items: center
  height: 100%
  

.sidebar
  margin-left: px(10)
  width: px(304)

.divider
  background-image: url('@/assets/images/devider.svg')
  background-repeat: no-repeat
  width: px(2)
  height: px(898)
  margin-left: px(10)

.menuList
  list-style: none
  padding: 0
  margin: 0

.parentItem
  margin-bottom: px(20)
  
  &:last-child
    margin-bottom: 0

.parentLabel
  color: rgba(216, 240, 255, 0.7)
  font-size: px(35)
  font-weight: 400
  line-height: px(41)  
  font-family: youshe
  padding: 0 px(20)    
  cursor: default
  text-align: center
  background-image: url('@/assets/images/activeBg.png')
  background-size: 150% 100%
  width: px(304)
  height: px(41)
  background-repeat: no-repeat
  background-position: center px(15)
  margin-bottom: px(10)
  display: flex        
  align-items: center  
  justify-content: center 
  transform: translateY(-8px) 

.childList
  list-style: none
  padding: 0
  margin: 0

.childItem
  color: rgba(216, 240, 255, 0.7)
  font-size: px(20)
  padding: px(8) px(20)
  font-family: youshe
  cursor: pointer
  transition: all 0.3s
  text-align: center
  margin-bottom: px(10)
  
  &:hover
    color: rgba(216, 240, 255, 0.9)
  
  &.active
    color: white
