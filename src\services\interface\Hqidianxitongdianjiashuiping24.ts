// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/GasElectricityPrice/delete */
export async function deleteUsingDelete5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE5Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasElectricityPrice/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/GasElectricityPrice/save */
export async function saveUsingPost5(
  body: API.GasElectricityPrice,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasElectricityPrice/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询一天（24小时） GET /masc/GasElectricityPrice/selectOneDay */
export async function selectOneDayUsingGet(options?: { [key: string]: any }) {
  return request<number[]>('/masc/GasElectricityPrice/selectOneDay', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/GasElectricityPrice/update */
export async function updateUsingPut5(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT5Params,
  body: API.GasElectricityPrice,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/GasElectricityPrice/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}
