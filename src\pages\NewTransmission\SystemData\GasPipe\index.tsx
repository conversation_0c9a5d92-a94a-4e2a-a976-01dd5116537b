import ChartTitle from '@/components/ui/chartTitle';
import _interface from '@/services/interface';
import px from '@/utils/px';
import {
  Button,
  Empty,
  Form,
  Input,
  Popconfirm,
  Space,
  Table,
  Upload,
  message,
} from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.sass';

const { uploadUsingPost6, pageUsingGet4, deleteUsingDelete8, updateUsingPut8 } =
  _interface.tianranqixitongguandao;

const EditableCell = ({
  editing,
  dataIndex,
  title,
  record,
  children,
  ...restProps
}: any) => {
  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[{ required: true, message: `请输入${title}!` }]}
          initialValue={record[dataIndex]}
        >
          <Input />
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

const Index = () => {
  const [dataSource, setDataSource] = useState<API.GasPipe[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingKey, setEditingKey] = useState<string>('');
  const [form] = Form.useForm();
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = (await pageUsingGet4({
        current: 1,
        size: 100,
      })) as ResponseData<API.PageGasPipe>;

      if (response.code === 1000 && response.data?.records) {
        setDataSource(response.data.records);
      }
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };
  const handleEdit = (record: API.GasPipe) => {
    form.setFieldsValue({
      pipe: record.pipe,
      fnode: record.fnode,
      tnode: record.tnode,
      c: record.c,
      m2: record.m2,
      diameter: record.diameter,
      length: record.length,
    });
    setEditingKey(record.id);
  };

  const handleCancel = () => {
    setEditingKey('');
  };

  const handleSave = async (record: API.GasPipe) => {
    try {
      const row = await form.validateFields();
      await updateUsingPut8({ id: record.id }, { ...record, ...row });
      message.success('保存成功');
      setEditingKey('');
      fetchData();
    } catch (error) {
      message.error('保存失败');
      console.error('Save failed:', error);
    }
  };

  const handleDelete = async (record: API.GasPipe) => {
    try {
      await deleteUsingDelete8({ id: record.id });
      message.success('删除成功');
      fetchData();
    } catch (error) {
      message.error('删除失败');
      console.error('Delete failed:', error);
    }
  };

  const isEditing = (record: any) => record.id === editingKey;

  const columns = [
    {
      title: '管道',
      dataIndex: 'pipe',
      key: 'pipe',
      editable: true,
    },
    {
      title: '起始节点',
      dataIndex: 'fnode',
      key: 'fnode',
      editable: true,
    },
    {
      title: '终止节点',
      dataIndex: 'tnode',
      key: 'tnode',
      editable: true,
    },
    {
      title: 'C',
      dataIndex: 'c',
      key: 'c',
      editable: true,
    },
    {
      title: '平方米',
      dataIndex: 'm2',
      key: 'm2',
      editable: true,
    },
    {
      title: '直径',
      dataIndex: 'diameter',
      key: 'diameter',
      editable: true,
    },
    {
      title: '长度',
      dataIndex: 'length',
      key: 'length',
      editable: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_: any, record: API.GasPipe) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button type="link" onClick={() => handleSave(record)}>
              保存
            </Button>
            <Button type="link" onClick={handleCancel}>
              取消
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              type="link"
              disabled={editingKey !== ''}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这条记录吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger>
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    fetchData();
  }, []);

  const customUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('file', file);

      await uploadUsingPost6(formData as any);
      onSuccess('OK');
      await fetchData();
    } catch (error) {
      onError(error);
      console.error('Upload failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.systemData}>
      <div className={styles.table}>
        <div className={styles.chartTitle}>
          <ChartTitle
            title="管道"
            width={px(600)}
            height={px(37)}
            right={
              <Upload customRequest={customUpload}>
                <Button>上传</Button>
              </Upload>
            }
          />
        </div>
        <div style={{ marginTop: px(20) }}>
          {dataSource.length > 0 ? (
            <Form form={form} component={false}>
              <Table
                components={{
                  body: {
                    cell: EditableCell,
                  },
                }}
                columns={columns.map((col) => {
                  if (!col.editable) {
                    return col;
                  }
                  return {
                    ...col,
                    onCell: (record: API.GasPipe) => ({
                      record,
                      dataIndex: col.dataIndex,
                      title: col.title,
                      editing: isEditing(record),
                    }),
                  };
                })}
                dataSource={dataSource}
                loading={loading}
                rowKey="id"
              />
            </Form>
          ) : (
            <Empty description="没有数据，请上传数据" />
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
