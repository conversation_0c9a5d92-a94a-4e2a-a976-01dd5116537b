import React, { useState } from 'react';
import styles from './index.module.sass';
import CustomPane from '@/components/CustomPane';
import ReactECharts from 'echarts-for-react';
import px from '@/utils/px';
const Statistics: React.FC = () => {

  const [isCalculating, setIsCalculating] = useState(false);
  const [buttonText, setButtonText] = useState('导入数据');
  const [isPulse, setIsPulse] = useState(false);

  const [formData, setFormData] = useState({
    batteryCapacity: 60, // 电池容量 kWh
    maxChargingPower: 7, // 最大充电功率 kW
    initialSoc: 30, // 初始电量状态 %
    targetSoc: 80  // 目标电量状态 %
  });

  const [evaluationResults, setEvaluationResults] = useState({
    peakLoadReduction: 5.2, // 高峰负荷削峰量 kW
    loadTransfer: 12.8, // 负荷转移量 kWh
    costReduction: 15.6 // 用电成本减少量 元
  });

  // 功率变化曲线配置
  const powerChangeOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00aeff',
      borderWidth: 1,
      textStyle: { color: '#fff' }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0:00', '4:00', '8:00', '12:00', '16:00', '20:00', '24:00'],
      axisLine: { lineStyle: { color: '#00aeff' } },
      axisLabel: { color: '#6affeb' },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      name: '功率 (kW)',
      nameTextStyle: { color: '#6affeb' },
      axisLine: { lineStyle: { color: '#00aeff' } },
      axisLabel: { color: '#6affeb' },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 174, 255, 0.2)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '充电功率',
        type: 'line',
        data: [0, 0, 7, 5, 0, 6, 0],
        smooth: true,
        lineStyle: { width: 3, color: '#00aeff' },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: { color: '#00aeff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 174, 255, 0.4)' },
              { offset: 1, color: 'rgba(0, 174, 255, 0.05)' }
            ]
          }
        }
      },
      {
        name: '放电功率',
        type: 'line',
        data: [0, 0, 0, 0, -4, -3, 0],
        smooth: true,
        lineStyle: { width: 3, color: '#6affeb' },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: { color: '#6affeb' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(106, 255, 235, 0.4)' },
              { offset: 1, color: 'rgba(106, 255, 235, 0.05)' }
            ]
          }
        }
      }
    ]
  };

  // 微电状态变化曲线配置
  const microgridStateOption = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00f7ff',
      borderWidth: 1,
      textStyle: { color: '#fff' }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0:00', '4:00', '8:00', '12:00', '16:00', '20:00', '24:00'],
      axisLine: { lineStyle: { color: '#00f7ff' } },
      axisLabel: { color: '#6affeb' },
      splitLine: { show: false }
    },
    yAxis: {
      type: 'value',
      name: 'SOC (%)',
      nameTextStyle: { color: '#6affeb' },
      axisLine: { lineStyle: { color: '#00f7ff' } },
      axisLabel: { color: '#6affeb' },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 247, 255, 0.2)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: 'SOC变化',
        type: 'line',
        data: [30, 30, 65, 80, 65, 50, 50],
        smooth: true,
        lineStyle: { width: 3, color: '#00f7ff' },
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: { color: '#00f7ff' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 247, 255, 0.4)' },
              { offset: 1, color: 'rgba(0, 247, 255, 0.05)' }
            ]
          }
        }
      }
    ]
  };

  const handleInputChange = (field: keyof typeof formData, value: number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleImportData = () => {
    setIsCalculating(true);
    setButtonText('导入中...');

    setTimeout(() => {
      setButtonText('导入完成');
      setIsPulse(true);

      // 模拟计算结果更新
      setEvaluationResults({
        peakLoadReduction: 5.2 + Math.random() * 2,
        loadTransfer: 12.8 + Math.random() * 3,
        costReduction: 15.6 + Math.random() * 5
      });

      setTimeout(() => {
        setIsPulse(false);
        setButtonText('重新导入');
        setIsCalculating(false);
      }, 2000);
    }, 1500);
  };

  return (
    <div className={styles.electrolyticAluminumCalculator}>
      <div className={styles.gridOverlay}></div>
      <div className={`${styles.glow} ${styles.glow1}`}></div>
      <div className={`${styles.glow} ${styles.glow2}`}></div>

      <div className={styles.container}>
        {/* 左侧输入面板 */}
        <div className={styles.inputPanel}>
          <CustomPane title="电动汽车参数">
            <div className={styles.inputSection}>
              <div className={styles.inputGrid}>
                <div className={styles.inputGroup}>
                  <label>电池容量 (kWh)</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={formData.batteryCapacity}
                      onChange={(e) => handleInputChange('batteryCapacity', Number(e.target.value))}
                    />
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>最大充电功率 (kW)</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={formData.maxChargingPower}
                      onChange={(e) => handleInputChange('maxChargingPower', Number(e.target.value))}
                    />
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>初始电量状态 (%)</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={formData.initialSoc}
                      onChange={(e) => handleInputChange('initialSoc', Number(e.target.value))}
                    />
                  </div>
                </div>

                <div className={styles.inputGroup}>
                  <label>目标电量状态 (%)</label>
                  <div className={styles.inputWrapper}>
                    <input
                      type="number"
                      value={formData.targetSoc}
                      onChange={(e) => handleInputChange('targetSoc', Number(e.target.value))}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className={styles.calculateSection}>
              <button
                className={`${styles.calculateButton} ${isPulse ? styles.pulse : ''}`}
                onClick={handleImportData}
                disabled={isCalculating}
              >
                {buttonText}
              </button>
            </div>
          </CustomPane>
        </div>
              {/* 右侧图表面板 */}
        <div className={styles.chartPanel}>
          <div className={styles.chartContainer}>
            <CustomPane title="功率变化曲线">
         
              <ReactECharts
                option={powerChangeOption}
                style={{ height: '100%', width: '100%' }}
                className="chart"
              />
          
            </CustomPane>
          </div>

          <div className={styles.chartContainer}>
            <CustomPane title="微电状态变化曲线">
            
                <ReactECharts
                  option={microgridStateOption}
                  style={{ height: '100%', width: '100%' }}
                  className="chart"
                />
             
            </CustomPane>
          </div>
        </div>

        {/* 中间评估指标面板 */}
        <div className={styles.middlePanel}>
          <CustomPane title="单体电动汽车响应能力评估指标">
            <div className={styles.indicatorsGrid}>
              <div className={styles.indicatorItem}>
                <div className={styles.indicatorLabel}>高峰负荷削峰量</div>
                <div className={styles.indicatorValue}>
                  {evaluationResults.peakLoadReduction.toFixed(1)}
                  <span className={styles.unit}>kW</span>
                </div>
              </div>

              <div className={styles.indicatorItem}>
                <div className={styles.indicatorLabel}>负荷转移量</div>
                <div className={styles.indicatorValue}>
                  {evaluationResults.loadTransfer.toFixed(1)}
                  <span className={styles.unit}>kWh</span>
                </div>
              </div>

              <div className={styles.indicatorItem}>
                <div className={styles.indicatorLabel}>用电成本减少量</div>
                <div className={styles.indicatorValue}>
                  {evaluationResults.costReduction.toFixed(1)}
                  <span className={styles.unit}>元</span>
                </div>
              </div>
            </div>
          </CustomPane>
        </div>

  
      </div>
    </div>
  );
};

export default Statistics;
