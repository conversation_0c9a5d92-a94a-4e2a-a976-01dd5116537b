import CustomButton from '@/components/CustomButton';
import CustomModal from '@/components/CustomModal';
import CustomTable from '@/components/CustomTable';
import {
  deleteUsingDELETE5,
  pageUsingGET3,
  saveUsingPOST5,
  updateUsingPUT5,
  uploadUsingPOST5,
} from '@/services/homepage/zhuyedianlijiedian';
import px from '@/utils/px';
import { CloudUploadOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Space,
  Upload,
} from 'antd';
import type { RcFile } from 'antd/es/upload';
import React, { useEffect, useState } from 'react';
import styles from './index.sass';

interface Props {
  name?: string;
  infoOpen: boolean;
  onCancelInfo: () => void;
}

interface TableRecord {
  busI: number;
  type: number;
  pd: number;
  qd: number;
  gs: number;
  bs: number;
  area: number;
  vm: number;
  va: number;
  baseKv: number;
  zone: number;
  id: number;
  vmin: number;
  vmax: number;
}

const Index: React.FC<Props> = (props: Props) => {
  const { infoOpen, onCancelInfo } = props;
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<TableRecord | null>(null);
  const [tableData, setTableData] = useState<TableRecord[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();

  const fetchTableData = async (page: number = 1) => {
    try {
      const response = await pageUsingGET3({
        current: page,
        size: 6,
      });
      if (response.code === 1000) {
        setTableData(response.data.records);
        setTotalRecords(response.data.total);
      }
    } catch (error) {
      message.error('获取数据失败');
    }
  };

  useEffect(() => {
    if (infoOpen) {
      fetchTableData();
    }
  }, [infoOpen]);

  const handleEdit = (record: TableRecord) => {
    setCurrentRecord(record);
    form.setFieldsValue({
      nodeNumber: record.busI,
      type: record.type,
      activePower: record.pd,
      reactivePower: record.qd,
      conductance: record.gs,
      susceptance: record.bs,
      area: record.area,
      voltagePeak: record.vm,
      va: record.va,
      baseVoltage: record.baseKv,
      zone: record.zone,
      maxVoltage: record.vmax,
      minVoltage: record.vmin,
    });
    setEditModalVisible(true);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const apiValues = {
        id: currentRecord?.id,
        busI: values.nodeNumber,
        type: values.type,
        pd: values.activePower,
        qd: values.reactivePower,
        gs: values.conductance,
        bs: values.susceptance,
        area: values.area,
        vm: values.voltagePeak,
        va: values.va,
        baseKv: values.baseVoltage,
        zone: values.zone,
        vmax: values.maxVoltage,
        vmin: values.minVoltage,
      };

      const response = await updateUsingPUT5(
        { id: currentRecord?.id },
        apiValues,
      );

      if (response.code === 1000) {
        message.success('保存成功');
        setEditModalVisible(false);
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('Save failed:', error);
      message.error('保存失败');
    }
  };

  const handleDelete = async (record: TableRecord) => {
    try {
      const response = await deleteUsingDELETE5({
        id: record.id,
      });

      if (response.code === 1000) {
        message.success('删除成功');
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('Delete failed:', error);
      message.error('删除失败');
    }
  };

  const handleAdd = () => {
    addForm.resetFields();
    setAddModalVisible(true);
  };

  const handleAddSave = async () => {
    try {
      const values = await addForm.validateFields();
      const apiValues = {
        busI: Number(values.busI),
        type: Number(values.type),
        pd: Number(values.pd),
        qd: Number(values.qd),
        gs: Number(values.gs),
        bs: Number(values.bs),
        area: Number(values.area),
        vm: Number(values.vm),
        va: Number(values.va),
        baseKv: Number(values.baseKv),
        zone: Number(values.zone),
        vmax: Number(values.vmax),
        vmin: Number(values.vmin),
      };

      const response = await saveUsingPOST5(apiValues);

      if (response.code === 1000) {
        message.success('添加成功');
        setAddModalVisible(false);
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '添加失败');
      }
    } catch (error) {
      console.error('Add failed:', error);
      message.error('添加失败');
    }
  };

  const handleUpload = async (file: RcFile) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await uploadUsingPOST5(formData);

      if (response.code === 1000) {
        message.success('上传成功');
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '上传失败');
      }
      return false;
    } catch (error) {
      console.error('Upload failed:', error);
      message.error('上传失败');
      return false;
    }
  };

  const formItems = (
    <Row gutter={16}>
      <Col span={8}>
        <Form.Item
          label="节点编号"
          name="busI"
          rules={[{ required: true, message: '请输入节点编号' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="类型"
          name="type"
          rules={[{ required: true, message: '请输入类型' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="有功功率"
          name="pd"
          rules={[{ required: true, message: '请输入有功功率' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="无功功率"
          name="qd"
          rules={[{ required: true, message: '请输入无功功率' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="电导"
          name="gs"
          rules={[{ required: true, message: '请输入电导' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="电纳"
          name="bs"
          rules={[{ required: true, message: '请输入电纳' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="区域"
          name="area"
          rules={[{ required: true, message: '请输入区域' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="电压峰值"
          name="vm"
          rules={[{ required: true, message: '请输入电压峰值' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="Va"
          name="va"
          rules={[{ required: true, message: '请输入Va' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="基准电压"
          name="baseKv"
          rules={[{ required: true, message: '请输入基准电压' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="Zone"
          name="zone"
          rules={[{ required: true, message: '请输入Zone' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="最大电压"
          name="vmax"
          rules={[{ required: true, message: '请输入最大电压' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="最小电压"
          name="vmin"
          rules={[{ required: true, message: '请输入最小电压' }]}
        >
          <Input />
        </Form.Item>
      </Col>
    </Row>
  );

  const columns = [
    { title: '节点编号', dataIndex: 'busI', key: 'busI' },
    { title: '类型', dataIndex: 'type', key: 'type' },
    { title: '有功功率', dataIndex: 'pd', key: 'pd' },
    { title: '无功功率', dataIndex: 'qd', key: 'qd' },
    { title: '电导', dataIndex: 'gs', key: 'gs' },
    { title: '电纳', dataIndex: 'bs', key: 'bs' },
    { title: '区域', dataIndex: 'area', key: 'area' },
    { title: '电压峰值', dataIndex: 'vm', key: 'vm' },
    { title: 'Va', dataIndex: 'va', key: 'va' },
    { title: '基准电压', dataIndex: 'baseKv', key: 'baseKv' },
    { title: 'Zone', dataIndex: 'zone', key: 'zone' },
    { title: '最大电压', dataIndex: 'vmax', key: 'vmax' },
    { title: '最小电压', dataIndex: 'vmin', key: 'vmin' },
    {
      title: '操作',
      key: 'operation',
      render: (_: any, record: TableRecord) => (
        <Space>
          <Button
            style={{ fontSize: px(14) }}
            type="link"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger style={{ fontSize: px(14) }}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      <CustomModal
        open={infoOpen}
        title="电力节点"
        onCancel={onCancelInfo}
        footer={null}
        width={'fit-content'}
        className={styles.customModal}
        centered={true}
      >
        <div className={styles.box}>
          <div style={{ display: 'flex' }}>
            <CustomButton icon={<PlusOutlined />} onClick={handleAdd}>
              添加
            </CustomButton>
            <Upload beforeUpload={handleUpload} showUploadList={false}>
              <CustomButton icon={<CloudUploadOutlined />}>导入</CustomButton>
            </Upload>
          </div>
          <CustomTable
            className={styles.customTable}
            columns={columns}
            dataSource={tableData}
            scroll={{ x: 'max-content' }}
            pagination={{
              current: currentPage,
              pageSize: 6,
              total: totalRecords,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              onChange: (page) => {
                setCurrentPage(page);
                fetchTableData(page);
              },
            }}
          />
        </div>
      </CustomModal>

      {/* 编辑弹窗 */}
      <Modal
        title="编辑节点"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={handleSave}
        width={px(1200)}
      >
        <Form form={form} layout="vertical" initialValues={currentRecord}>
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>

      {/* 添加弹窗 */}
      <Modal
        title="添加节点"
        open={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onOk={handleAddSave}
        width={px(1200)}
      >
        <Form form={addForm} layout="vertical">
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>
    </div>
  );
};

export default Index;
