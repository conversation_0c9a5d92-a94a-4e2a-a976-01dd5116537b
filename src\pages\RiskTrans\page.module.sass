@import "~@/assets/css/helper.sass"

.container 
  display: flex
  padding: px(20) px(100) 
  gap: px(40)
  height: 100vh
  
  .left
    width: px(500)
  .center
    height: 90%
    display: flex
    align-items: center
 
     
      
       
  .right
    width: px(1100)
 
 


.leftContent
  padding: px(20)
  background: rgba(255, 255, 255, 0.05)
  border-radius: px(8)
 
  border: px(1) solid rgba(255, 255, 255, 0.3)
  
  .formRow
    display: flex
    gap: px(20)
    margin-bottom: px(20)
    
    &:last-child
      margin-bottom: 0
    
  .input
    flex: 1
    display: flex
    align-items: center
    gap: px(10)
    
    .inputTitle
      font-size: px(18)
      min-width: px(100)
      color: rgba(255, 255, 255, 0.8)
    
    .inputValue
      flex: 1
      
      :global(.ant-form-item)
        margin-bottom: 0
.ant-spin-nested-loading
  flex: 1
  
  .ant-spin-container
    height: 100%
    display: flex
    flex-direction: column
    
    .ant-table
      flex: 1
      
      .ant-table-container
        height: 100%
        
        .ant-table-body
          max-height: calc(100vh - #{px(200)})
          overflow-y: auto
    
    .ant-pagination
      margin: px(16) 0
      flex-shrink: 0
      
      .ant-pagination-item,
      .ant-pagination-prev,
      .ant-pagination-next,
      .ant-pagination-jump-prev,
      .ant-pagination-jump-next
        background: transparent
        border-color: rgba(255, 255, 255, 0.2)
        a
          color: rgba(255, 255, 255, 0.8)
        
        &:hover
          border-color: #00aeff
          a
            color: #00aeff
            
      .ant-pagination-item-active
        background: rgba(0, 174, 255, 0.2)
        border-color: #00aeff
        border: 1px solid #00aeff
        a
          color: #00aeff
          
      .ant-select-selector
        background: transparent
        border-color: rgba(255, 255, 255, 0.2)
        color: rgba(255, 255, 255, 0.8)
        
      .ant-pagination-options-quick-jumper
        color: rgba(255, 255, 255, 0.8)
        input
          background: transparent
          border-color: rgba(255, 255, 255, 0.2)
          color: rgba(255, 255, 255, 0.8)
          &:hover, &:focus
            border-color: #00aeff
.chartContainer
  display: flex
  gap: px(20)
  .resultchart
    width: px(500)
  
.chart, .resultchart
  margin-top: px(40)
  padding: px(10)
  background: rgba(255, 255, 255, 0.05)
  border-radius: px(8)
  border: px(1) solid rgba(255, 255, 255, 0.3)
.spinContainer
  position: absolute
  top: px(380)
  left: 35%
  transform: translateX(-50%)
  :global(.ant-spin-dot-item)  // 设置 Spin 组件的颜色
    background-color: #6affeb

.tablesContainer
  display: flex
  gap: px(20)
  margin-bottom: px(5)
  :global(.ant-table-wrapper)
    flex: 1