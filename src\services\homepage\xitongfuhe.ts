// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/SystemLoad/delete */
export async function deleteUsingDELETE16(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE16Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/SystemLoad/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/SystemLoad/save */
export async function saveUsingPOST16(
  body: API.SystemLoad,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/SystemLoad/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 查询一天（24小时） GET /masc/SystemLoad/selectOneDay */
export async function selectOneDayUsingGET1(options?: { [key: string]: any }) {
  return request<number[]>('/masc/SystemLoad/selectOneDay', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/SystemLoad/update */
export async function updateUsingPUT16(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT16Params,
  body: API.SystemLoad,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/SystemLoad/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/SystemLoad/upload */
export async function uploadUsingPOST16(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/SystemLoad/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
