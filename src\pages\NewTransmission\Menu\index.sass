@import "~@/assets/css/helper.sass"
.box
    padding: px(20) px(30) px(20) px(30)
    height: px(1000)
    background: transparent
    color: white
    :global
        .ant-menu
            width: px(300)
            font-size: px(18) 
        .ant-menu-submenu-title
            font-size: px(20)
        .ant-menu-light
            background: linear-gradient(90.00deg, rgba(11, 38, 71, 0.2) 2.328%,rgba(137, 188, 255, 0.09) 42.832%,rgba(163, 207, 255, 0) 100%)
            color: white
            .ant-menu-item-group-title
                color: white
                font-size: px(18)
            .ant-menu-item:not(.ant-menu-item-selected):not(ant-menu-submenu-selected):hover,
            .ant-menu-submenu-selected >.ant-menu-submenu-title
                color: #1EC2FF
            .ant-menu-item-selected
                color: white
                background: linear-gradient(90.00deg, rgba(11, 38, 71, 0.6) 2.328%,rgba(70, 147, 255, 0.498) 42.832%,rgba(163, 209, 255, 0) 100%)
        .ant-menu-light.ant-menu-root.ant-menu-inline
                border: none
        .ant-menu-light .ant-menu-item
            color: white
        .ant-menu-light .ant-menu-submenu-title
            color: white 
        
        