import Sidebar from '@/components/SideBar';
import { sidebarList } from '@/utils/sidebar.js';
import { useState } from 'react';
import styles from './index.sass';
import { Result, RunData, SystemData } from './index.styled-components';

export default function Page() {
  const [activeId, setActiveId] = useState(sidebarList[0].key);

  const contentMap = {
    sdata: <SystemData />,
    ydata: <RunData />,
    result: <Result />,
  };

  return (
    <div className={styles.container}>
      <Sidebar menu={sidebarList} activeId={activeId} onChange={setActiveId} />
      <div className={styles.content}>{contentMap[activeId]}</div>
    </div>
  );
}
