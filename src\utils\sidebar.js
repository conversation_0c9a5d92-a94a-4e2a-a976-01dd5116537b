export const sidebarList = [
  { key: 'sdata', label: '系统数据' },
  { key: 'ydata', label: '运行数据' },
  { key: 'result', label: '结果展示' },
];
export const warningControlSidebarList = [
  { key: 'netData', label: '场景模拟' },
  { key: 'scene', label: '数据输入' },

  { key: 'warningResult', label: '风险预警' },
];
export const riskTransSidebarList = [
  { key: 'scene', label: '场景生成' },
  { key: 'clearResult', label: '出清结果' },
];
export const sidebarKeys = sidebarList.map((item) => item.key);
export const sidebarMap = sidebarList.reduce(
  (acc, item) => ({ ...acc, [item.key]: item }),
  {},
);
