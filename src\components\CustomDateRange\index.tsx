import { myLocale } from '@/utils/utils';
import { DatePicker } from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import React from 'react';
import styles from './index.sass';

const CustomInput: React.FC<RangePickerProps> = (props) => {
  return (
    <DatePicker.RangePicker
      {...props}
      locale={myLocale}
      className={`${styles.box} ${props.className}`}
    />
  );
};

export default CustomInput;
