// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/NewEnergyOutput/delete */
export async function deleteUsingDELETE15(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE15Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/NewEnergyOutput/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/NewEnergyOutput/page */
export async function pageUsingGET9(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGET9Params,
  options?: { [key: string]: any },
) {
  return request<API.PageNewEnergyOutput>('/masc/NewEnergyOutput/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 单条插入 POST /masc/NewEnergyOutput/save */
export async function saveUsingPOST15(
  body: API.NewEnergyOutput,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/NewEnergyOutput/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/NewEnergyOutput/update */
export async function updateUsingPUT15(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT15Params,
  body: API.NewEnergyOutput,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/NewEnergyOutput/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/NewEnergyOutput/upload */
export async function uploadUsingPOST15(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/NewEnergyOutput/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
