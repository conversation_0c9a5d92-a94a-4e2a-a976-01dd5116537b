@import "~@/assets/css/helper.sass"

.container 
  display: flex
  // :global(.ant-table-tbody)
  //   > tr
  //     &:nth-child(odd)
  //       > td
  //         background-color: black !important
  //         color: white // 确保文字在黑色背景上可见

  //     &:nth-child(even)
  //         background-image: url('@/assets/images/tbbg2.png'), url('@/assets/images/texture.svg')  !important
  //         background-size: 100% 100%, 10%, 100%
  //         background-repeat: no-repeat, repeat-x
  //         background-position: center,top left

  //     &:hover
  //       > td
  //         background-color: rgba(0, 0, 0, 0.1) !important
  //         background-image: none !important



.systemData
  width: px(1600)
  height: px(900)
  display: flex
  justify-content: space-around
  align-items: center

.runData
  width: px(1600)
  height: px(900)
  display: flex
  flex-direction: column
  .table
    height: px(400)
    .content
      display: flex
      justify-content: space-around
      width: 100%
      height: px(330)
      .chart
        width: 40%
        position: relative

.charts
    display: flex
    width: 100%
    justify-content: space-around
    margin-top: px(72)
    .chart
        width: px(718)
        margin-right: px(29)
        .chartTitle
            height: px(52)
            line-height: px(52)
        .chartContent
            margin-top: px(39)
            width: px(718)
            height: px(420)
  
.runContainer
  display: flex
  flex-direction: column
  gap: px(50) 
  margin-top: px(100)
       
   
  .newchart
    width: px(1600)
    height: px(300)
.chartTitle
  text-align: center
  font-family: youshe
  color: #ffffff
  font-size: px(22)
  text-shadow: px(1) px(1) px(5) #055978 , -1px 1px 2px #1EC2FF, 1px -1px 2px #1EC2FF, -1px 1px 2px #1EC2FF