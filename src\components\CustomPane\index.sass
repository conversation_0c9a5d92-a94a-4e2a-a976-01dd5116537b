@import "~@/assets/css/helper.sass"

.box
    width: 100%
    height: 100%
    display: flex
    flex-direction: column    

.title
    color: #e2ebf1
    font-size: px(20)
    font-weight: bold
    font-family: 微软雅黑 Light
    display: flex
.titleBg
    width: px(20)
    height: px(20)
    background-image: url(@/assets/newlayout/title.png)
    background-repeat: no-repeat
    background-size: 50% 100%
.titleB
    width: 100%
    height: px(5)
    background-image: url(@/assets/newlayout/titlebt.png)
    background-repeat: no-repeat
    background-size: 100% 100%
    margin: px(5) 0 px(10) 0
.button
    width: px(110)
    height: px(25)
    padding: px(5)
    color: #e2ebf1
    font-weight: 300
    font-size: px(14)
    text-align: center
    background-image: url(@/assets/newlayout/button.png)
    background-repeat: no-repeat
    background-size: 100% 100%
    cursor: pointer
    z-index: 10000000000000000000000000
    &:hover
        background-image: url(@/assets/newlayout/buttonh.png)
        background-repeat: no-repeat
        background-size: 100% 100%
