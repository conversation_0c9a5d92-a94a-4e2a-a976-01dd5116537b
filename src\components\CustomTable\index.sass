@import "~@/assets/css/helper.sass"
.table
    :global
        .ant-table-thead >tr>th
            color: rgba(106, 255, 235, 0.95)
            border-bottom: px(1) rgba(106, 255, 235, 0.5) dashed
            font-size: px(16)
            font-weight: bolder
            text-align: center
            padding: px(24) px(5)
            background: rgba(6, 43, 69, 0.8) !important

        .ant-table-row:nth-child(odd)
            background: rgba(6, 43, 69, 0.3)

        .ant-table-tbody >tr >td
            color: rgba(106, 255, 235, 0.75)
            border-bottom: px(1) rgba(106, 255, 235, 0.3) dashed
            font-size: px(16)
            text-align: center
            font-weight: 500
            padding: px(18) px(5) !important

        :where(.css-dev-only-do-not-override-190m0jy).ant-table-wrapper,
        .ant-table-tbody > tr.ant-table-placeholder:hover > th,
        :where(.css-dev-only-do-not-override-190m0jy).ant-table-wrapper,
        .ant-table-tbody > tr.ant-table-placeholder:hover > td,
        :where(.css-dev-only-do-not-override-190m0jy).ant-table-wrapper,
        .ant-table-tbody > tr.ant-table-placeholder
            background: rgba(6, 43, 69, 0.8)
        
        .ant-empty-description
            color: rgba(106, 255, 235, 0.75)
        
        .ant-table-content
            cursor: pointer
            .ant-table-cell-fix-left-last
                background: rgba(6, 43, 69, 0.8)
            &::-webkit-scrollbar
                background-color: rgba(6, 43, 69, 0.5)
                height: px(10)
            &::-webkit-scrollbar-thumb
                background-color: rgba(106, 255, 235, 0.3)
                border-radius: px(5)

        .ant-table-tbody >tr.ant-table-row:hover>td,
        .ant-table-tbody >tr >td.ant-table-cell-row-hover
            background: rgba(106, 255, 235, 0.05)

        .ant-table-cell-fix-right,
        .ant-table-cell-fix-left
            background: rgba(6, 43, 69, 0.8) !important
            color: rgba(106, 255, 235, 0.75)

        .ant-pagination-options
            display: none
        .ant-pagination,
        .ant-pagination-jump-next,
        .ant-pagination-prev,
        .ant-pagination-next
            color: rgba(106, 255, 235, 0.75)
            font-size: px(16)
        
        .ant-pagination-next,
        .ant-pagination-prev,
        .ant-pagination-item,
        .ant-pagination-item-link,
        .ant-pagination-jump-next,
        .ant-pagination-jump-prev,
        .ant-pagination-item-active
            background: transparent
            height: px(30)
            line-height: px(30)
            
        .ant-pagination-item:hover
            background: transparent !important
            border: px(2) solid rgba(106, 255, 235, 0.5)
            a
                color: rgba(106, 255, 235, 0.85)

        .ant-pagination-item-active
            border-color: rgba(106, 255, 235, 0.5)
            a
                color: rgba(106, 255, 235, 0.85)

        .ant-pagination .ant-pagination-item a
            color: rgba(106, 255, 235, 0.75)
:global
  .small-font
    font-size: px(8)
    // 如果表头也需要同样大小
    th
      font-size: px(8)
    td
      font-size: px(8)