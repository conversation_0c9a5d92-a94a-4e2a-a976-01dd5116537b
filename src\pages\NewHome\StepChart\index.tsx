import { findByGensetNumberUsingGET } from '@/services/homepage/shouyeranmeijizubaojiabaoliangfenduan';
import px from '@/utils/px';
import * as echarts from 'echarts';
import React, { useEffect, useRef } from 'react';

interface StepChartProps {
  className?: string;
  gensetNumber?: number;
}

const StepChart: React.FC<StepChartProps> = ({ gensetNumber = 10 }) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!chartRef.current) return;

      const myChart = echarts.init(chartRef.current);

      try {
        const response = await findByGensetNumberUsingGET({
          gensetNumber: gensetNumber,
        });

        if (response.data) {
          const { quote, capacity } = response.data;

          // 处理数据，构建阶梯图所需的数据格式
          const data = [];
          let accumulatedCapacity = 0;

          // 添加起点
          data.push([0, quote[0] * 7.28]);

          // 构建阶梯图的数据点
          for (let i = 0; i < quote.length; i++) {
            // 当前价格点
            data.push([accumulatedCapacity, quote[i] * 7.28]);
            accumulatedCapacity += capacity[i];
            // 下一个价格点（如果不是最后一个点）
            if (i < quote.length - 1) {
              data.push([accumulatedCapacity, quote[i] * 7.28]);
            } else {
              // 最后一个点
              data.push([accumulatedCapacity, quote[i] * 7.28]);
            }
          }

          const option = {
            title: {
              text: ' ',
              textStyle: {
                color: 'white',
                fontSize: px(14),
              },
              left: 'center',
            },
            tooltip: {
              trigger: 'axis',
              formatter: function (params: any) {
                return `累计报量：${params[0].data[0]}<br/>报价：${params[0].data[1]}`;
              },
              axisPointer: {
                type: 'cross',
              },
            },
            grid: {
              top: '10%',
              left: '3%',
              right: '4%',
              bottom: '10%',
              containLabel: true,
            },
            xAxis: {
              type: 'value',
              name: '累计报量(MW)',
              nameLocation: 'middle',
              nameGap: px(25),
              nameTextStyle: {
                color: 'white',
                fontSize: px(12),
                padding: [px(10), 0, 0, 0],
              },
              axisLabel: {
                color: 'white',
                fontSize: px(12),
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#062b45',
                  type: 'dashed',
                },
              },
            },
            yAxis: {
              type: 'value',
              name: '报价(元/MWh)',
              nameTextStyle: {
                color: 'white',
                fontSize: px(12),
              },
              axisLabel: {
                color: 'white',
                fontSize: px(12),
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#062b45',
                  type: 'dashed',
                },
              },
            },
            series: [
              {
                name: '报价曲线',
                type: 'line',
                step: 'right',
                data: data,
                lineStyle: {
                  color: 'rgb(39, 202, 255)',
                  width: 2,
                },
                itemStyle: {
                  color: 'rgb(39, 202, 255)',
                },
                areaStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: 'rgba(39, 202, 255, 0.3)',
                      },
                      {
                        offset: 1,
                        color: 'rgba(39, 202, 255, 0)',
                      },
                    ],
                  },
                },
              },
            ],
          };

          myChart.setOption(option);
        }
      } catch (error) {
        console.error('Failed to fetch data:', error);
      }
    };

    fetchData();

    return () => {
      if (chartRef.current) {
        echarts.getInstanceByDom(chartRef.current)?.dispose();
      }
    };
  }, [gensetNumber]);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }} />;
};

export default StepChart;
