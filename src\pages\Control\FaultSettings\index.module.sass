@import "~@/assets/css/helper.sass"

// 变量定义
$primaryDark: #0a1525
$secondaryDark: rgba(76, 76, 76, 0.153)
$accentBlue: #00aeff
$accentGreen: #6affeb
$accentCyan: #00f7ff
$textPrimary: rgba(255, 255, 255, 0.9)
$textSecondary: rgba(255, 255, 255, 0.6)
$cardBg: rgba(255, 255, 255, 0.05)
$cardBorder: rgba(0, 174, 255, 0.2)
$hoverBg: rgba(255, 255, 255, 0.1)
$activeBg: rgba(0, 174, 255, 0.2)

// 全局重置
*
  margin: 0
  padding: 0
  box-sizing: border-box
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif
  font-size: px(18)

.airConditioningCalculator
  color: $textPrimary
  min-height: 100vh
  overflow-x: hidden
  position: relative

// 网格背景
.gridOverlay
  position: fixed
  top: 0
  left: 0
  width: 100%
  height: 100%
  pointer-events: none
  z-index: 0

// 主容器
.container
  display: grid
  grid-template-columns: px(400) 1fr
  gap: px(20)
  max-width: px(1800)
  margin: 0 auto
  padding: px(20)
  position: relative
  z-index: 10
  height: px(930)

// 左侧输入面板
.inputPanel
  width: px(400)
  display: flex
  flex-direction: column
  gap: px(20)
  overflow-y: auto

// 输入区域
.inputSection
  color: white
  margin-top: px(20)
  background: $cardBg
  border-radius: px(12)
  padding: px(20)
  border: px(1) solid $cardBorder

.inputGrid
  gap: px(15)

.inputGroup
  font-size: px(16)
  margin-bottom: px(15)

  label
    display: block
    margin-bottom: px(8)
    font-size: px(14)
    color: $textSecondary

.inputWrapper
  position: relative

.inputGroup input
  width: 100%
  padding: px(12) px(18)
  background: rgba(0, 0, 0, 0.3)
  border: px(1) solid $cardBorder
  border-radius: px(8)
  color: $textPrimary
  font-size: px(16)
  outline: none
  transition: all 0.3s ease

  &:focus
    border-color: $accentBlue
    box-shadow: 0 0 0 2px rgba(0, 174, 255, 0.2)

.units
  position: absolute
  right: px(12)
  top: 50%
  transform: translateY(-50%)
  color: $textSecondary
  font-size: px(14)

// 导入按钮
.importButton
  padding: px(8) px(20)
  background: linear-gradient(135deg, $accentBlue, $accentCyan)
  border: none
  border-radius: px(8)
  color: white
  font-size: px(14)
  font-weight: 600
  cursor: pointer
  transition: all 0.3s ease
  box-shadow: 0 px(4) px(15) rgba(0, 174, 255, 0.3)

  &:hover
    transform: translateY(px(-1))
    box-shadow: 0 px(6) px(20) rgba(0, 174, 255, 0.4)

  &:disabled
    opacity: 0.6
    cursor: not-allowed

  &.pulse
    animation: pulse 1s infinite

@keyframes pulse
  0%
    box-shadow: 0 0 0 0 rgba(0, 174, 255, 0.7)
  70%
    box-shadow: 0 0 0 px(8) rgba(0, 174, 255, 0)
  100%
    box-shadow: 0 0 0 0 rgba(0, 174, 255, 0)

// 评估指标网格 - 横向排列
.indicatorsGrid
  display: flex
  flex-direction: row
  gap: px(20)
  margin-bottom: px(30)
  justify-content: space-between

.indicatorItem
  background: $cardBg
  border-radius: px(12)
  padding: px(15) px(20)
  border: px(1) solid $cardBorder
  text-align: center
  flex: 1
  min-width: px(120)

.indicatorLabel
  font-size: px(12)
  color: $textSecondary
  margin-bottom: px(8)
  white-space: nowrap

.indicatorValue
  font-size: px(24)
  font-weight: 600
  color: $accentGreen
  display: flex
  align-items: baseline
  justify-content: center
  gap: px(4)

.unit
  font-size: px(14)
  color: $textSecondary

// 右侧输出结果面板
.chartPanel
  width: px(1100)
  height: px(880)
  display: flex
  flex-direction: column
  border-radius: px(16)
  box-shadow: 0 px(10) px(30) rgba(0, 0, 0, 0.3)
  border: px(1) solid $cardBorder
  backdrop-filter: blur(px(10))
  padding: px(25)
  overflow-y: auto

  :global
    .box
      background-color: rgba(255, 255, 255, 0.05)

// 图表区域
.chartsSection
  display: flex
 
  flex-direction: column
  gap: px(20)
  flex: 1

.chartContainer
  background: $cardBg
  border-radius: px(12)
  border: px(1) solid $cardBorder
  padding: px(15)
  flex: 1
  height: px(300)

.chartTitle
  font-size: px(16)
  color: $accentGreen
  margin-bottom: px(15)
  text-align: center
  font-weight: 600

// 发光效果
.glow
  position: fixed
  border-radius: 50%
  filter: blur(px(100))
  pointer-events: none
  z-index: 1

.glow1
  width: px(400)
  height: px(400)
  background: radial-gradient(circle, rgba(0, 174, 255, 0.15) 0%, transparent 70%)
  top: 20%
  left: 10%
  animation: float 6s ease-in-out infinite

.glow2
  width: px(300)
  height: px(300)
  background: radial-gradient(circle, rgba(106, 255, 235, 0.1) 0%, transparent 70%)
  bottom: 20%
  right: 15%
  animation: float 8s ease-in-out infinite reverse

@keyframes float
  0%, 100%
    transform: translateY(0px)
  50%
    transform: translateY(px(-20))
