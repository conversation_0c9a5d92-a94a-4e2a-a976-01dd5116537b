// LineChart.js
import px from '@/utils/px';
import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';

interface LineChartProps {
  xAxisData: string[];
  realPower: number[];
  predictPower: number[];
  xUnit?: string;
  yUnit?: string;
}

const LineChart: React.FC<LineChartProps> = ({
  xAxisData,
  realPower,
  predictPower,
  xUnit,
  yUnit,
}) => {
  const chartRef = useRef<HTMLDivElement>(null); // 设置初始值为 null

  useEffect(() => {
    if (!chartRef.current) {
      return;
    }

    // 初始化图表并指定主题
    const myChart = echarts.init(chartRef.current);

    const option = {
      tooltip: {
        trigger: 'axis',
        textStyle: {
          fontSize: px(16), // 设置 tooltip 中文本的字体大小
        },
      },
      grid: {
        top: '25%', // Add padding to the top of the chart area
        left: '5%',
        right: '13%',
        bottom: '0%',
        containLabel: true,
      },
      legend: {
        data: ['默认值', '选择灾害后'],
        textStyle: {
          color: '#fff', // Set legend label color to white
          fontSize: px(16),
        },
      },
      xAxis: [
        {
          name: xUnit,
          nameTextStyle: {
            color: '#fff', // Set y-axis label color to white
            fontSize: px(16),
          },
          type: 'category',
          data: xAxisData,
          interval: 30, // 控制每个类目之间的间隔
          axisPointer: {
            type: 'shadow',
          },
          axisLabel: {
            color: '#fff', // Set x-axis label color to white
            fontSize: px(16),
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: yUnit,
          nameTextStyle: {
            color: '#fff', // Set y-axis label color to white
            fontSize: px(16),
          },
          axisLabel: {
            formatter: '{value}',
            color: '#fff', // Set y-axis label color to white
            fontSize: px(16),
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#304A65',
              type: 'dashed',
            },
          },
        },
      ],
      series: [
        {
          name: '默认值',
          type: 'line',
          itemStyle: {
            color: '#1EC2FF',
          },
          data: realPower,
        },
        {
          name: '选择灾害后',
          type: 'line',
          itemStyle: {
            color: '#2687FF',
          },
          data: predictPower,
        },
      ],
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100,
        },
      ],
    };

    myChart.setOption(option);

    return () => {
      myChart.dispose();
    };
  }, [realPower, predictPower, xUnit, yUnit]); // 仅在这些依赖项发生变化时重新渲染

  return <div ref={chartRef} style={{ height: '100%', width: '100%' }} />;
};

export default LineChart;
