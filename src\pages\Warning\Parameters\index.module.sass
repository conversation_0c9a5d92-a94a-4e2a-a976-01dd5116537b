@import "~@/assets/css/helper.sass"

// 变量定义
$primaryDark: #0a1525
$secondaryDark: rgba(1, 34, 77, 0.369)
$accentBlue: #00aeff
$accentGreen: #6affeb
$accentCyan: #00f7ff
$textPrimary: rgba(255, 255, 255, 0.9)
$textSecondary: rgb(255, 255, 255)
$cardBg: rgba(255, 255, 255, 0.05)
$cardBorder: rgba(0, 174, 255, 0.2)
$hoverBg: rgba(255, 255, 255, 0.1)
$activeBg: rgba(0, 174, 255, 0.2)

// 全局重置
*
  margin: 0
  padding: 0
  box-sizing: border-box
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif
  font-size: px(18)

.electrolyticAluminumCalculator
 
  color: $textPrimary
  min-height: 100vh
  overflow-x: hidden
  position: relative
  

// 网格背景
.gridOverlay
  position: fixed
  top: 0
  left: 0
  width: 100%
  height: 100%
  
  pointer-events: none
  z-index: 0

// 主容器
.container
  display: grid
  grid-template-rows: px(360) 1fr auto
  gap: px(20)
  min-width: px(1570)
  margin: 0 auto
  padding: px(20)
  position: relative
  z-index: 10
  height: calc(100vh - px(40))
  max-height: px(900)

// 头部
.header
  grid-column: 1 / -1
  background: $secondaryDark
  border-radius: px(16)
  padding: px(20) px(25)
  display: flex
  justify-content: space-between
  align-items: center
  box-shadow: 0 px(10) px(30) rgba(0, 0, 0, 0.3)
  border: px(1) solid $cardBorder
  margin-bottom: px(20)

  h1
    font-size: px(30)
    font-weight: 600
    background: linear-gradient(90deg, $accentGreen, $accentCyan)
    -webkit-background-clip: text
    -webkit-text-fill-color: transparent
    letter-spacing: 0.5px

// 顶部输入面板
.inputPanel
  display: grid
  grid-template-columns: 1fr 1fr 1fr
  gap: px(20)
  overflow-x: auto

.panelTitle
  font-size: px(24)
  margin-bottom: px(15)
  color: $accentCyan
  display: flex
  align-items: center
  gap: px(12)

  i
    font-size: px(26)

// 输入区域
.inputSection
  color: white
  margin-top: px(10)
  background: $cardBg
  border-radius: px(12)
  padding: px(12)
  border: px(1) solid $cardBorder

.sectionTitle
  font-size: px(24)
  margin-bottom: px(20)
  color: $accentGreen
  display: flex
  align-items: center
  gap: px(10)

.inputGrid
  gap: px(12)

.inputGroup
  font-size: px(14)
  margin-bottom: px(12)

  label
    display: block
    margin-bottom: px(6)
    font-size: px(12)
    color: $textSecondary

.inputWrapper
  position: relative

.inputGroup input
  width: 100%
  padding: px(6) px(10)
  background: rgba(0, 0, 0, 0.3)
  border: px(1) solid $cardBorder
  border-radius: px(6)
  color: $textPrimary
  font-size: px(12)
  outline: none
  transition: all 0.3s ease

  &:focus
    border-color: $accentBlue
    box-shadow: 0 0 0 3px rgba(0, 174, 255, 0.2)

.units
  position: absolute
  right: px(8)
  top: 50%
  transform: translateY(-50%)
  color: $textSecondary
  font-size: px(10)

// 中间图表面板
.chartPanel
  display: flex
  flex-direction: column
  gap: px(20)
  width: px(720)
  overflow-y: auto

.chartContainer
  background: rgba(21, 54, 91, 0.317)
  border-radius: px(16)
  padding: px(20)
  box-shadow: 0 px(10) px(30) rgba(0, 0, 0, 0.407)
  border: px(1) solid $cardBorder
  width: 100%
  height: px(300)
  position: relative
  overflow: hidden

.chartTitle
  font-size: px(18)
  margin-bottom: px(15)
  color: $accentGreen
  display: flex
  align-items: center
  gap: px(10)

.chart
  width: 100%
  height: px(350)

// 中间图表面板
.chartPanel
  border-radius: px(16)
  box-shadow: 0 px(10) px(30) rgba(0, 0, 0, 0.3)
  border: px(1) solid $cardBorder
  backdrop-filter: blur(px(10))
  flex: 1
  min-height: px(400)

.chartContainer
  height: 100%
  padding: px(20)

// 底部结果面板
.resultPanel
  background: $secondaryDark
  border-radius: px(16)
  padding: px(10)
  box-shadow: 0 px(10) px(30) rgba(0, 0, 0, 0.518)
  border: px(1) solid $cardBorder
  display: grid
  grid-template-columns: repeat(4, 1fr)
  gap: px(20)

.resultItem
  display: flex
  flex-direction: column
  justify-content: center
  align-items: center
  background: $cardBg
  border: px(1) solid $cardBorder
  border-radius: px(12)
  height: px(80)
  padding: px(15)
  transition: all 0.3s ease
  text-align: center

  &.highlight
    background: rgba(0, 174, 255, 0.15)
    border-color: $accentBlue
    box-shadow: 0 px(5) px(15) rgba(0, 174, 255, 0.2)

.resultName
  font-size: px(14)
  color: $textSecondary
  margin-bottom: px(8)
  white-space: nowrap

.resultValue
  font-size: px(24)
  color: $accentGreen
  font-weight: 600
  font-weight: 700
  background: linear-gradient(90deg, $accentGreen, $accentBlue)
  -webkit-background-clip: text
  -webkit-text-fill-color: transparent
  margin-bottom: px(5)

.resultChange
  font-size: px(16)
  color: $accentGreen
  display: flex
  align-items: center
  gap: px(5)

// 按钮样式
.btnContainer
  height: px(30)
  width: px(500)
  font-size: px(20)
  display: flex
  justify-content: center
  margin-top: px(10)

.calculateBtn
  background: linear-gradient(90deg, $accentGreen, $accentBlue)
  color: $primaryDark
  border: none
  border-radius: px(10)
  padding: px(5) px(10)
  font-size: px(16)
  font-weight: 600
  cursor: pointer
  transition: all 0.3s ease
  box-shadow: 0 px(5) px(15) rgba(0, 174, 255, 0.3)

  &:hover:not(:disabled)
    transform: translateY(px(-3))
    box-shadow: 0 px(8) px(20) rgba(0, 174, 255, 0.5)

  &:active
    transform: translateY(px(1))

  &:disabled
    cursor: not-allowed
    opacity: 0.7

// 装饰元素
.glow
  position: absolute
  width: px(300)
  height: px(300)
  border-radius: px(50)
  background: radial-gradient(circle, rgba(106, 255, 235, 0.2) 0%, transparent 70%)
  filter: blur(px(30))
  z-index: 1

.glow1
  top: -px(100)
  left: -px(100)

.glow2
  bottom: -px(100)
  right: -px(100)
  background: radial-gradient(circle, rgba(0, 174, 255, 0.2) 0%, transparent 70%)

// 动画关键帧
@keyframes pulse
  0%
    transform: scale(1)
    box-shadow: 0 0 0 0 rgba(106, 255, 235, 0.7)
  70%
    transform: scale(1.03)
    box-shadow: 0 0 0 15px rgba(106, 255, 235, 0)
  100%
    transform: scale(1)
    box-shadow: 0 0 0 0 rgba(106, 255, 235, 0)

 