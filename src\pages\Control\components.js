import LineBarChart from '@/components/chart/LineBarChart';
import MonitorTab from '@/components/MonitorTab';
import ChartTitle from '@/components/ui/chartTitle';
import px from '@/utils/px';
import { Input } from '@chakra-ui/react';
import { Button, Checkbox, Table } from 'antd';
import { useState } from 'react';
import { SystemData } from '../Transmission/index.styled-components';
import styles from './page.module.sass';
export const InputGroup = ({
  left,
  right,
  disabled = false,
  value,
  className,
  onChange,
}) => {
  return (
    <div className={styles.inputContainer}>
      <span className={styles.leftlabel}>{left}</span>
      <div className={styles.input}>
        <Input
          className={className}
          variant="normal"
          disabled={disabled}
          value={value || ''}
          onChange={onChange}
        />
      </div>
      <span className={styles.rightlabel}>{right}</span>
    </div>
  );
};

export const Sdata = () => {
  return <SystemData />;
};
export const RunData = () => {
  const numericArray3 = [
    [96, 56],
    [4, 27],
    [35, 58],
    [39, 43],
    [44, 14],
    [86, 49],
    [64, 92],
    [45, 82],
  ];
  const node_columns = [
    {
      title: '节点编号',
      dataIndex: 'nodeId',
      key: 'nodeId',
    },
    {
      title: '气体上限',
      dataIndex: 'upperLimit',
      key: 'upperLimit',
    },
    {
      title: '气体下限',
      dataIndex: 'lowerLimit',
      key: 'lowerLimit',
    },
    {
      title: '气压荷需求',
      dataIndex: 'pressureDemand',
      key: 'pressureDemand',
    },
  ];

  const node_data = [
    {
      key: '1',
      nodeId: 67,
      upperLimit: 96,
      lowerLimit: 89,
      pressureDemand: 92,
    },
    {
      key: '2',
      nodeId: 75,
      upperLimit: 21,
      lowerLimit: 59,
      pressureDemand: 51,
    },
    {
      key: '3',
      nodeId: 39,
      upperLimit: 70,
      lowerLimit: 66,
      pressureDemand: 11,
    },
    {
      key: '4',
      nodeId: 90,
      upperLimit: 63,
      lowerLimit: 40,
      pressureDemand: 23,
    },
    {
      key: '5',
      nodeId: 100,
      upperLimit: 54,
      lowerLimit: 40,
      pressureDemand: 8,
    },
    { key: '6', nodeId: 1, upperLimit: 79, lowerLimit: 93, pressureDemand: 93 },
    {
      key: '7',
      nodeId: 65,
      upperLimit: 35,
      lowerLimit: 38,
      pressureDemand: 20,
    },
    {
      key: '8',
      nodeId: 59,
      upperLimit: 59,
      lowerLimit: 90,
      pressureDemand: 86,
    },
    {
      key: '9',
      nodeId: 65,
      upperLimit: 35,
      lowerLimit: 38,
      pressureDemand: 20,
    },
    {
      key: '10',
      nodeId: 59,
      upperLimit: 59,
      lowerLimit: 90,
      pressureDemand: 86,
    },
    {
      key: '11',
      nodeId: 65,
      upperLimit: 35,
      lowerLimit: 38,
      pressureDemand: 20,
    },
  ];
  const category = [
    '2022-01-01',
    '2022-01-02',
    '2022-01-03',
    '2022-01-04',
    '2022-01-05',
    '2022-01-06',
    '2022-01-07',
  ];

  const lineData = [120, 132, 101, 134, 90, 230, 210];

  const barData = [80, 90, 70, 60, 10, 100, 80];
  return (
    <div className={styles.runData}>
      <div className={styles.table}>
        <div className={styles.chartTitle}>
          <ChartTitle
            title="负荷曲线"
            width={px(600)}
            height={px(37)}
            right={<Button>上传</Button>}
          />
        </div>
        <div style={{ marginTop: px(20) }} className={styles.content}>
          <div className={styles.chart}>
            <LineBarChart
              category={category}
              lineData={lineData}
              barData={barData}
              xlabelName="时间"
              ylabelName="价格"
            />
          </div>
          <div className={styles.chart}>
            <Table
              columns={node_columns}
              dataSource={node_data}
              pagination={{
                pageSize: 6,
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export const Ydata = () => {
  const items = [
    // {
    //   key: '1',
    //   label: '天然气系统',
    //   children: <RunData />,
    // },
    {
      key: '2',
      label: '电气系统',
      children: <RunData />,
    },
  ];

  return (
    <div className={styles.RDContainer}>
      <MonitorTab defaultActiveKey="1" items={items} />
    </div>
  );
};

export const Decision = () => {
  const [elect, setElect] = useState(1);
  const [gas, setGas] = useState(1);
  const [restrain, setRestrain] = useState(1);
  return (
    <div className={styles.mainDecision}>
      <div className={styles.title}>能源系统关键参数</div>
      <div className={styles.content}>
        <InputGroup
          left="电力失负荷价值"
          disabled={false}
          value={elect}
          right="￥/MWH"
          className={styles.customInput}
          onChange={(e) => setElect(e.target.value)}
        />
        <InputGroup
          left="天然气失负荷价值"
          disabled={false}
          value={gas}
          right="￥/m3"
          className={styles.customInput}
          onChange={(e) => setGas(e.target.value)}
        />
        <InputGroup
          left="可靠性约束"
          disabled={false}
          value={restrain}
          right="MWH"
          className={styles.customInput}
          onChange={(e) => setRestrain(e.target.value)}
        />
      </div>

      <div className={styles.title}>燃气机组参数</div>
      <div className={styles.content}>
        <div className={styles.checkBox}>
          <div className={styles.linetext}>
            <Checkbox
              isDisabled={false}
              onChange={(e) => {
                console.log('change');
              }}
            />
            发电量
          </div>
          <div className={styles.linetext}>
            <Checkbox
              isDisabled={false}
              onChange={(e) => {
                console.log('change');
              }}
            />
            旋转备用量
          </div>
        </div>
      </div>

      <div className={styles.title}>燃煤机组参数</div>
      <div className={styles.content}>
        <div className={styles.checkBox}>
          <div className={styles.linetext}>
            <Checkbox
              isDisabled={false}
              onChange={(e) => {
                console.log('change');
              }}
            />
            发电量
          </div>
          <div className={styles.linetext}>
            <Checkbox
              isDisabled={false}
              onChange={(e) => {
                console.log('change');
              }}
            />
            旋转备用量
          </div>
        </div>
      </div>
    </div>
  );
};

export const Result = () => {
  return (
    <div className={styles.box}>
      <div className={styles.center}>
        <div className={styles.title}>旋转备用量 </div>
        <div>
          <div className={styles.mytable}>
            <div className={styles.tableHeader}>
              <div className={styles.headerFirstCell}>时间/h</div>
              {[...Array(12).keys()].map((i) => (
                <div key={`header-${i}`} className={styles.headerCell}>
                  {i + 1}
                </div>
              ))}
            </div>
            <div className={styles.tableBody}>
              <div className={styles.bodyRow}>
                <div className={styles.bodyHeadCell} key="body-first-cell">
                  {' '}
                  电动汽车（MW）
                </div>
                {[...Array(12).keys()].map((i) => (
                  <div className={styles.bodyCell} key={`body-cell-${i}`}>
                    {i}
                  </div>
                ))}
              </div>
              <div className={styles.bodyRow}>
                <div className={styles.bodyHeadCell} key="body-second-cell">
                  {' '}
                  温控负荷（MW）
                </div>
                {[...Array(12).keys()].map((i) => (
                  <div className={styles.bodyCell} key={`body-cell-${i}`}>
                    {i}
                  </div>
                ))}
              </div>

              <div className={styles.bodyRow}>
                <div className={styles.bodyHeadCell} key="body-second-cell">
                  {' '}
                  工业负荷（MW）
                </div>
                {[...Array(12).keys()].map((i) => (
                  <div className={styles.bodyCell} key={`body-cell-${i}`}>
                    {i}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.center}>
        <div className={styles.title}>{'发电量'} </div>
        <div>
          <div className={styles.mytable}>
            <div className={styles.tableHeader}>
              <div className={styles.headerFirstCell}>时间/h</div>
              {[...Array(12).keys()].map((i) => (
                <div key={`header-${i}`} className={styles.headerCell}>
                  {i + 1}
                </div>
              ))}
            </div>
            <div className={styles.tableBody}>
              <div className={styles.bodyRow}>
                <div className={styles.bodyHeadCell} key="body-first-cell">
                  {' '}
                  电动汽车（MW）
                </div>
                {[...Array(12).keys()].map((i) => (
                  <div className={styles.bodyCell} key={`body-cell-${i}`}>
                    {i}
                  </div>
                ))}
              </div>
              <div className={styles.bodyRow}>
                <div className={styles.bodyHeadCell} key="body-second-cell">
                  {' '}
                  温控负荷（MW）
                </div>
                {[...Array(12).keys()].map((i) => (
                  <div className={styles.bodyCell} key={`body-cell-${i}`}>
                    {i}
                  </div>
                ))}
              </div>

              <div className={styles.bodyRow}>
                <div className={styles.bodyHeadCell} key="body-second-cell">
                  {' '}
                  工业负荷（MW）
                </div>
                {[...Array(12).keys()].map((i) => (
                  <div className={styles.bodyCell} key={`body-cell-${i}`}>
                    {i}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
