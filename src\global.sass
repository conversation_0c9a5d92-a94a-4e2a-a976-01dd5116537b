@font-face
    font-family: 'zihun'
    src: url('./assets/fonts/zihun.ttf') format('truetype')
@font-face
    font-family: 'youshe'
    src: url('./assets/fonts/youshebiaotihei.ttf') format('truetype')
@font-face 
  font-family: 'gdhxd'
  src: url('./assets/fonts/gdhxd.ttf') format('truetype')
  font-weight: normal
  font-style: normal

:global
  .ant-btn
    background: rgba(6, 43, 69, 0.8) !important
    border: 1px rgba(106, 255, 235, 0.5) dashed !important
    color: rgba(106, 255, 235, 0.85) !important
    
    &:hover
      background: rgba(6, 43, 69, 0.9) !important
      border-color: rgba(106, 255, 235, 0.8) !important
      color: rgba(106, 255, 235, 0.95) !important
    
    &:active
      background: rgba(6, 43, 69, 1) !important
      border-color: rgba(106, 255, 235, 0.9) !important
      color: rgba(106, 255, 235, 1) !important
    
    &:disabled
      background: rgba(6, 43, 69, 0.5) !important
      border-color: rgba(106, 255, 235, 0.3) !important
      color: rgba(106, 255, 235, 0.5) !important
      cursor: not-allowed

  // 主要按钮样式
  .ant-btn-primary
    background: rgba(106, 255, 235, 0.2) !important
    
    &:hover
      background: rgba(106, 255, 235, 0.3) !important
    
    &:active
      background: rgba(106, 255, 235, 0.4) !important

  // 危险按钮样式
  .ant-btn-dangerous
    background: rgba(255, 77, 79, 0.2) !important
    border-color: rgba(255, 77, 79, 0.5) !important
    color: rgba(255, 77, 79, 0.85) !important
    
    &:hover
      background: rgba(255, 77, 79, 0.3) !important
      border-color: rgba(255, 77, 79, 0.8) !important
      color: rgba(255, 77, 79, 0.95) !important