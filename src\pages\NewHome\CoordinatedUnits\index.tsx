import CustomButton from '@/components/CustomButton';
import CustomModal from '@/components/CustomModal';
import CustomTable from '@/components/CustomTable';
import {
  deleteUsingDELETE17,
  pageUsingGET10,
  saveUsingPOST17,
  updateUsingPUT17,
  uploadUsingPOST17,
} from '@/services/homepage/shouyetongdiaojizu';
import px from '@/utils/px';
import { CloudUploadOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Space,
  Upload,
} from 'antd';
import type { RcFile } from 'antd/es/upload';
import React, { useEffect, useState } from 'react';
import styles from './index.sass';

interface Props {
  name?: string;
  infoOpen: boolean;
  onCancelInfo: () => void;
}

interface TableRecord {
  genNumber: number;
  nodeNumber: number;
  pg: number;
  qg: number;
  vg: number;
  status: number;
  id: number;
  pmin: number;
  qmax: number;
  mbase: number;
  pmax: number;
  qmin: number;
}

const Index: React.FC<Props> = (props: Props) => {
  const { infoOpen, onCancelInfo } = props;
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<TableRecord | null>(null);
  const [tableData, setTableData] = useState<TableRecord[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();

  const fetchTableData = async (page: number = 1) => {
    try {
      const response = await pageUsingGET10({
        current: page,
        size: 6,
      });
      if (response.code === 1000) {
        setTableData(response.data.records);
        setTotalRecords(response.data.total);
      }
    } catch (error) {
      message.error('获取数据失败');
    }
  };

  useEffect(() => {
    if (infoOpen) {
      fetchTableData();
    }
  }, [infoOpen]);

  const handleEdit = (record: TableRecord) => {
    setCurrentRecord(record);
    form.setFieldsValue({
      unitNumber: record.genNumber,
      nodeNumber: record.nodeNumber,
      activePower: record.pg,
      reactivePower: record.qg,
      maxReactivePower: record.qmax,
      minReactivePower: record.qmin,
      workingVoltage: record.vg,
      powerBase: record.mbase,
      workingStatus: record.status,
      maxActivePower: record.pmax,
      minActivePower: record.pmin,
    });
    setEditModalVisible(true);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const apiValues = {
        id: currentRecord?.id,
        genNumber: values.unitNumber,
        nodeNumber: values.nodeNumber,
        pg: values.activePower,
        qg: values.reactivePower,
        qmax: values.maxReactivePower,
        qmin: values.minReactivePower,
        vg: values.workingVoltage,
        mbase: values.powerBase,
        status: values.workingStatus,
        pmax: values.maxActivePower,
        pmin: values.minActivePower,
      };

      const response = await updateUsingPUT17(
        { id: currentRecord?.id },
        apiValues,
      );

      if (response.code === 1000) {
        message.success('保存成功');
        setEditModalVisible(false);
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('Save failed:', error);
      message.error('保存失败');
    }
  };

  const handleDelete = async (record: TableRecord) => {
    try {
      const response = await deleteUsingDELETE17({
        id: record.id,
      });

      if (response.code === 1000) {
        message.success('删除成功');
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('Delete failed:', error);
      message.error('删除失败');
    }
  };

  const handleAdd = () => {
    addForm.resetFields();
    setAddModalVisible(true);
  };

  const handleAddSave = async () => {
    try {
      const values = await addForm.validateFields();
      const apiValues = {
        genNumber: Number(values.unitNumber),
        nodeNumber: Number(values.nodeNumber),
        pg: Number(values.activePower),
        qg: Number(values.reactivePower),
        qmax: Number(values.maxReactivePower),
        qmin: Number(values.minReactivePower),
        vg: Number(values.workingVoltage),
        mbase: Number(values.powerBase),
        status: Number(values.workingStatus),
        pmax: Number(values.maxActivePower),
        pmin: Number(values.minActivePower),
      };

      const response = await saveUsingPOST17(apiValues);

      if (response.code === 1000) {
        message.success('添加成功');
        setAddModalVisible(false);
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '添加失败');
      }
    } catch (error) {
      console.error('Add failed:', error);
      message.error('添加失败');
    }
  };

  const handleUpload = async (file: RcFile) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await uploadUsingPOST17(formData);

      if (response.code === 1000) {
        message.success('上传成功');
        fetchTableData(currentPage);
      } else {
        message.error(response.msg || '上传失败');
      }
      return false;
    } catch (error) {
      console.error('Upload failed:', error);
      message.error('上传失败');
      return false;
    }
  };

  const columns = [
    { title: '机组编号', dataIndex: 'genNumber', key: 'genNumber' },
    { title: '节点编号', dataIndex: 'nodeNumber', key: 'nodeNumber' },
    { title: '有功功率', dataIndex: 'pg', key: 'pg' },
    { title: '无功功率', dataIndex: 'qg', key: 'qg' },
    { title: '无功最大值', dataIndex: 'qmax', key: 'qmax' },
    { title: '无功最小值', dataIndex: 'qmin', key: 'qmin' },
    { title: '工作电压', dataIndex: 'vg', key: 'vg' },
    { title: '功率基准值', dataIndex: 'mbase', key: 'mbase' },
    { title: '工作状态', dataIndex: 'status', key: 'status' },
    { title: '有功最大', dataIndex: 'pmax', key: 'pmax' },
    { title: '有功最小', dataIndex: 'pmin', key: 'pmin' },
    {
      title: '操作',
      key: 'operation',
      render: (_: any, record: TableRecord) => (
        <Space>
          <Button
            style={{ fontSize: px(14) }}
            type="link"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条记录吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger style={{ fontSize: px(14) }}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const formItems = (
    <Row gutter={8}>
      <Col span={8}>
        <Form.Item
          label="机组编号"
          name="unitNumber"
          rules={[{ required: true, message: '请输入机组编号' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="节点编号"
          name="nodeNumber"
          rules={[{ required: true, message: '请输入节点编号' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="有功功率"
          name="activePower"
          rules={[{ required: true, message: '请输入有功功率' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="无功功率"
          name="reactivePower"
          rules={[{ required: true, message: '请输入无功功率' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="无功最大值"
          name="maxReactivePower"
          rules={[{ required: true, message: '请输入无功最大值' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="无功最小值"
          name="minReactivePower"
          rules={[{ required: true, message: '请输入无功最小值' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="工作电压"
          name="workingVoltage"
          rules={[{ required: true, message: '请输入工作电压' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="功率基准值"
          name="powerBase"
          rules={[{ required: true, message: '请输入功率基准值' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="工作状态"
          name="workingStatus"
          rules={[{ required: true, message: '请输入工作状态' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="有功最大"
          name="maxActivePower"
          rules={[{ required: true, message: '请输入有功最大' }]}
        >
          <Input />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          label="有功最小"
          name="minActivePower"
          rules={[{ required: true, message: '请输入有功最小' }]}
        >
          <Input />
        </Form.Item>
      </Col>
    </Row>
  );

  return (
    <div className={styles.container}>
      <CustomModal
        open={infoOpen}
        title="统调机组"
        onCancel={onCancelInfo}
        footer={null}
        width={'fit-content'}
        className={styles.customModal}
        centered={true}
      >
        <div className={styles.box}>
          <div style={{ display: 'flex' }}>
            <CustomButton icon={<PlusOutlined />} onClick={handleAdd}>
              添加
            </CustomButton>
            <Upload beforeUpload={handleUpload} showUploadList={false}>
              <CustomButton icon={<CloudUploadOutlined />}>导入</CustomButton>
            </Upload>
          </div>
          <CustomTable
            className={styles.customTable}
            columns={columns}
            dataSource={tableData}
            pagination={{
              current: currentPage,
              pageSize: 6,
              total: totalRecords,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              onChange: (page) => {
                setCurrentPage(page);
                fetchTableData(page);
              },
            }}
          />
        </div>
      </CustomModal>

      {/* 编辑弹窗 */}
      <Modal
        title="编辑节点"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        onOk={handleSave}
        width={px(1000)}
      >
        <Form form={form} layout="vertical" initialValues={currentRecord}>
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>

      {/* 添加弹窗 */}
      <Modal
        title="添加节点"
        open={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        onOk={handleAddSave}
        width={px(1000)}
      >
        <Form form={addForm} layout="vertical">
          <div className={styles.formContainer}>{formItems}</div>
        </Form>
      </Modal>
    </div>
  );
};

export default Index;
