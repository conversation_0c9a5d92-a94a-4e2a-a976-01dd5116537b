import px from '@/utils/px';
import * as echarts from 'echarts';
import { useEffect } from 'react';
interface EnergyStorageChartProps {
  containerId: string;
  xData: number[];
  yData: number[];
}

const EnergyStorageChart: React.FC<EnergyStorageChartProps> = ({
  containerId,
  xData,
  yData,
}) => {
  useEffect(() => {
    const chartDom = document.getElementById(containerId);
    if (!chartDom) return;
    if (!chartDom) return;

    const myChart = echarts.init(chartDom);

    // 3D柱状图的偏移量
    const offsetX = 4;
    const offsetY = 2;

    // 绘制左侧面
    const CubeLeft = echarts.graphic.extendShape({
      shape: {
        x: 0,
        y: 0,
      },
      buildPath: function (ctx, shape) {
        const xAxisPoint = shape.xAxisPoint;
        const c0 = [shape.x, shape.y];
        const c1 = [shape.x - offsetX, shape.y - offsetY];
        const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY];
        const c3 = [xAxisPoint[0], xAxisPoint[1]];
        ctx
          .moveTo(c0[0], c0[1])
          .lineTo(c1[0], c1[1])
          .lineTo(c2[0], c2[1])
          .lineTo(c3[0], c3[1])
          .closePath();
      },
    });

    // 绘制右侧面
    const CubeRight = echarts.graphic.extendShape({
      shape: {
        x: 0,
        y: 0,
      },
      buildPath: function (ctx, shape) {
        const xAxisPoint = shape.xAxisPoint;
        const c1 = [shape.x, shape.y];
        const c2 = [xAxisPoint[0], xAxisPoint[1]];
        const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY];
        const c4 = [shape.x + offsetX, shape.y - offsetY];
        ctx
          .moveTo(c1[0], c1[1])
          .lineTo(c2[0], c2[1])
          .lineTo(c3[0], c3[1])
          .lineTo(c4[0], c4[1])
          .closePath();
      },
    });

    // 绘制顶面
    const CubeTop = echarts.graphic.extendShape({
      shape: {
        x: 0,
        y: 0,
      },
      buildPath: function (ctx, shape) {
        const c1 = [shape.x, shape.y];
        const c2 = [shape.x + offsetX, shape.y - offsetY];
        const c3 = [shape.x, shape.y - offsetX];
        const c4 = [shape.x - offsetX, shape.y - offsetY];
        ctx
          .moveTo(c1[0], c1[1])
          .lineTo(c2[0], c2[1])
          .lineTo(c3[0], c3[1])
          .lineTo(c4[0], c4[1])
          .closePath();
      },
    });

    // 注册三个面图形
    echarts.graphic.registerShape('CubeLeft', CubeLeft);
    echarts.graphic.registerShape('CubeRight', CubeRight);
    echarts.graphic.registerShape('CubeTop', CubeTop);

    const option = {
      title: {
        text: '储能类资源可调容量',
        textStyle: {
          color: '#FFFFFF',
          fontSize: px(16),
        },
        top: 10,
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        textStyle: {
          color: '#fafafa',
        },
        borderColor: 'transparent',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        extraCssText: 'backdrop-filter: blur(6px);',
        formatter: (params: any) => {
          const item = params[1];
          return item.name + '<br>储能SoC：' + item.value + ' kWh';
        },
      },
      grid: {
        top: '15%',
        left: '2%',
        right: '2.5%',
        bottom: '5%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xData,
        axisLine: {
          show: true,
          lineStyle: {
            width: 1,
            color: '#545454',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: px(14),
          color: '#FFFFFF',
        },
      },
      yAxis: {
        type: 'value',
        name: '储能SoC(kWh)',
        nameTextStyle: {
          color: '#fff',
          fontWeight: 400,
          fontSize: px(14),
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          fontSize: px(14),
          color: '#FFFFFF',
        },
      },
      series: [
        {
          type: 'custom',
          renderItem: (params: any, api: any) => {
            const location = api.coord([api.value(0), api.value(1)]);
            return {
              type: 'group',
              children: [
                {
                  type: 'CubeLeft',
                  shape: {
                    api,
                    xValue: api.value(0),
                    yValue: api.value(1),
                    x: location[0],
                    y: location[1],
                    xAxisPoint: api.coord([api.value(0), 0]),
                  },
                  style: {
                    fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: '#6AFFEB',
                      },
                      {
                        offset: 0.35,
                        color: '#6AFFEB',
                      },
                      {
                        offset: 1,
                        color: 'transparent',
                      },
                    ]),
                  },
                },
                {
                  type: 'CubeRight',
                  shape: {
                    api,
                    xValue: api.value(0),
                    yValue: api.value(1),
                    x: location[0],
                    y: location[1],
                    xAxisPoint: api.coord([api.value(0), 0]),
                  },
                  style: {
                    fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: '#00BEAA',
                      },
                      {
                        offset: 0.35,
                        color: '#00BEAA',
                      },
                      {
                        offset: 1,
                        color: 'transparent',
                      },
                    ]),
                  },
                },
                {
                  type: 'CubeTop',
                  shape: {
                    api,
                    xValue: api.value(0),
                    yValue: api.value(1),
                    x: location[0],
                    y: location[1],
                    xAxisPoint: api.coord([api.value(0), 0]),
                  },
                  style: {
                    fill: '#03FFB1',
                  },
                },
              ],
            };
          },
          data: yData,
        },
        {
          type: 'bar',
          itemStyle: {
            color: 'transparent',
          },
          data: yData,
        },
      ],
    };

    myChart.setOption(option);

    return () => {
      myChart.dispose();
    };
  }, [containerId, xData, yData]);

  return <div id={containerId} style={{ width: '100%', height: '100%' }} />;
};

export default EnergyStorageChart;
