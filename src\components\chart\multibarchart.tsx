import * as echarts from 'echarts';
import { EChartOption } from 'echarts';
import { useEffect, useRef } from 'react';

const MultiBar = () => {
  const chartRef = useRef<HTMLDivElement>(null); // Explicitly type the ref

  useEffect(() => {
    if (chartRef.current) {
      const chart = echarts.init(chartRef.current);
      // Define the option with an explicit type
      const option: EChartOption = {
        // ... your chart option here, ensure the properties match expected types
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            name: '时间',
            data: [
              '1',
              '3',
              '5',
              '7',
              '9',
              '11',
              '13',
              '15',
              '17',
              '19',
              '21',
              '23',
            ],
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
        series: [
          {
            name: '<PERSON><PERSON>',
            type: 'bar',
            barWidth: 5,
            stack: 'Search Engine',
            emphasis: {
              focus: 'series',
            },
            data: [620, 732, 701, 734, 1090, 1130, 1120],
          },
          {
            name: 'Google',
            type: 'bar',
            stack: 'Search Engine',
            emphasis: {
              focus: 'series',
            },
            data: [120, 132, 101, 134, 290, 230, 220],
          },
          {
            name: 'Bing',
            type: 'bar',
            stack: 'Search Engine',
            emphasis: {
              focus: 'series',
            },
            data: [60, 72, 71, 74, 190, 130, 110],
          },
          {
            name: 'Others',
            type: 'bar',
            stack: 'Search Engine',
            emphasis: {
              focus: 'series',
            },
            data: [62, 82, 91, 84, 109, 110, 120],
          },
          {
            name: 'Average',
            type: 'line',
            data: [850, 932, 1001, 1154, 1190, 1330, 1410],
            // 在这里可以添加更多折线图的配置，如线条样式、颜色等
          },
        ],
      };
      chart.setOption(option);
      return () => chart.dispose();
    }
  }, []);

  return <div ref={chartRef} style={{ width: '300px', height: '300px' }}></div>;
};

export default MultiBar;
