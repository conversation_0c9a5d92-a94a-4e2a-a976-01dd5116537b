// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 根据id删除 DELETE /masc/UnifiedSchedulingGen/delete */
export async function deleteUsingDELETE17(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteUsingDELETE17Params,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/UnifiedSchedulingGen/delete', {
    method: 'DELETE',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 分页查询 GET /masc/UnifiedSchedulingGen/page */
export async function pageUsingGET10(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.pageUsingGET10Params,
  options?: { [key: string]: any },
) {
  return request<API.PageUnifiedSchedulingGen>(
    '/masc/UnifiedSchedulingGen/page',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 单条插入 POST /masc/UnifiedSchedulingGen/save */
export async function saveUsingPOST17(
  body: API.UnifiedSchedulingGen,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/UnifiedSchedulingGen/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 根据id修改数据 PUT /masc/UnifiedSchedulingGen/update */
export async function updateUsingPUT17(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.updateUsingPUT17Params,
  body: API.UnifiedSchedulingGen,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/UnifiedSchedulingGen/update', {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    data: body,
    ...(options || {}),
  });
}

/** 表格上传 POST /masc/UnifiedSchedulingGen/upload */
export async function uploadUsingPOST17(
  body: string,
  options?: { [key: string]: any },
) {
  return request<any>('/masc/UnifiedSchedulingGen/upload', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
