import * as echarts from 'echarts';
import { useEffect, useRef } from 'react';
const LineChart = ({ xAxisLabel, yAxisLabel }) => {
  const chartRef = useRef(null);

  useEffect(() => {
    // Ensure the div ref is set
    if (chartRef.current) {
      // Initialize the chart
      const myChart = echarts.init(chartRef.current, 'dark');

      // Chart configuration
      const option = {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',

        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7'],
          name: xAxisLabel,
          nameLocation: 'middle',
          nameGap: 30,
        },
        yAxis: {
          type: 'value',
          name: yAxisLabel, // Y Axis label
          nameLocation: 'middle', // position of the Y axis name
          nameGap: 40, // gap between the Y axis name and the axis line,
          splitLine: {
            lineStyle: {
              color: 'rgba(176, 215, 255, 0.25)', // Set the color of yAxis line
              type: 'dashed', // Set the yAxis line style to dashed
            },
          },
        },
        series: [
          {
            data: [820, 932, 901, 934, 1290, 1330, 1320],
            type: 'line',
            smooth: true,
          },
        ],
        grid: {
          left: '5%',

          containLabel: true,
        },
      };

      // Set the option for the chart
      myChart.setOption(option);

      // Dispose the chart if the component unmounts
      return () => myChart.dispose();
    }
  }, []);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }}></div>;
};

export default LineChart;
