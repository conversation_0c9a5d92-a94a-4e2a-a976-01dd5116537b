import * as echarts from 'echarts';
import { EChartOption } from 'echarts';
import React, { useEffect, useRef } from 'react';
interface StepLineChartProps {
  lineColor: string;
}
const StepLineChart: React.FC<StepLineChartProps> = ({ lineColor }) => {
  const chartRef = useRef<HTMLDivElement>(null); // Explicitly type the ref

  useEffect(() => {
    if (chartRef.current) {
      const chart = echarts.init(chartRef.current);
      // Define the option with an explicit type
      const option: EChartOption = {
        // ... your chart option here, ensure the properties match expected types
        title: {
          text: '',
          textStyle: { color: '#FFFFFF' },
        },
        tooltip: {
          trigger: 'axis',
          textStyle: { color: '#FFFFFF' },
        },
        legend: {
          data: ['Step Start', 'Step Middle', 'Step End'],
          textStyle: { color: '#FFFFFF' },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7'],
          axisLabel: { color: 'FFFFFF' },
          splitLine: {
            lineStyle: {
              color: 'rgba(176, 215, 255, 0.25)', // Set the color of yAxis line
              type: 'dashed', // Set the yAxis line style to dashed
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#FFFFFF' },
          splitLine: {
            lineStyle: {
              color: 'rgba(176, 215, 255, 0.25)', // Set the color of yAxis line
              type: 'dashed', // Set the yAxis line style to dashed
            },
          },
        },
        series: [
          {
            name: ' 期望失负荷值（兆瓦时）',
            type: 'line',
            step: 'start',
            data: [120, 132, 101, 134, 90, 230, 210],
            lineStyle: {
              color: lineColor,
            },
            symbol: 'none', // Set symbol to 'none' to remove data points
          },
        ],
      };
      chart.setOption(option);
      return () => chart.dispose();
    }
  }, []);

  return <div ref={chartRef} style={{ width: '100%', height: '100%' }}></div>;
};

export default StepLineChart;
