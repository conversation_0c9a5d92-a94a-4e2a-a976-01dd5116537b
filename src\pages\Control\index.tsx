import HierarchicalSidebar from '@/components/HierarchicalSidebar';
import { useState } from 'react';
import AfterResult from './AfterResult';
import FaultSettings from './FaultSettings';
import styles from './page.module.sass';
import Parameters from './Parameters';
import Statistics from './Statistics';

const warningControlSidebarList = [
  {
    key: 'pre',
    label: '负荷类型',
    children: [
      { key: 'parameters', label: '电解金属负荷' },
      { key: 'preResults', label: '电动汽车' },
      { key: 'faultSettings', label: '空调' },
    ],
  },
  // {
  //   key: 'post',
  //   label: '灾后再调度',
  //   children: [
  //     { key: 'faultSettings', label: '故障场景设置' },
  //     { key: 'postResults', label: '结果展示' },
  //   ],
  // },
];

export default function Page() {
  const [activeId, setActiveId] = useState('parameters');
  const sideMap = {
    parameters: <Parameters setActiveId={setActiveId} />,
    preResults: <Statistics />,
    faultSettings: <FaultSettings setActiveId={setActiveId} />,
    postResults: <AfterResult />,
  };

  return (
    <div className={styles.container}>
      <HierarchicalSidebar
        menu={warningControlSidebarList}
        activeId={activeId}
        onChange={setActiveId}
      />
      <div className={styles.content}>{sideMap[activeId]}</div>
    </div>
  );
}
