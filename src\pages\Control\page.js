'use client';

import useSidebar from '@/stores/sidebar';
import { Decision, Result, Sdata, Ydata } from './components';
import styles from './page.module.sass';

const componentsMap = {
  sdata: <Sdata />,
  ydata: <Ydata />,
  result: <Result />,
  decision: <Decision />,
};

export default function Page() {
  const { activeId } = useSidebar();
  return <div className={styles.page}>{componentsMap[activeId]}</div>;
}
