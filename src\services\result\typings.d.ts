declare namespace API {
  type callMainFunctionUsingGETParams = {
    /** kc */
    kc?: number;
  };

  type CoalElectricityPrice = {
    coalElectricityPrice?: number;
    date?: number;
    id?: number;
  };

  type CoalGen = {
    id?: number;
    installedCapacity?: number;
    mbase?: number;
    number?: number;
    pg?: number;
    pmax?: number;
    pmin?: number;
    qg?: number;
    qmax?: number;
    qmin?: number;
    status?: number;
    vg?: number;
  };

  type CoalPrice = {
    coalPrice?: number;
    date?: number;
    id?: number;
  };

  type deleteUsingDELETE10Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE11Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE12Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE13Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE14Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE15Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE16Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE17Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE1Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE2Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE3Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE4Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE5Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE6Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE7Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE8Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETE9Params = {
    /** id */
    id: number;
  };

  type deleteUsingDELETEParams = {
    /** id */
    id: number;
  };

  type DistributedSmallCoalGasGen = {
    genNumber?: number;
    hour1?: number;
    hour10?: number;
    hour11?: number;
    hour12?: number;
    hour13?: number;
    hour14?: number;
    hour15?: number;
    hour16?: number;
    hour17?: number;
    hour18?: number;
    hour19?: number;
    hour2?: number;
    hour20?: number;
    hour21?: number;
    hour22?: number;
    hour23?: number;
    hour24?: number;
    hour3?: number;
    hour4?: number;
    hour5?: number;
    hour6?: number;
    hour7?: number;
    hour8?: number;
    hour9?: number;
    id?: number;
  };

  type EleGenset = {
    id?: number;
    mbase?: number;
    number?: number;
    pg?: number;
    pmax?: number;
    pmin?: number;
    qg?: number;
    qmax?: number;
    qmin?: number;
    status?: number;
    type?: string;
    vg?: number;
  };

  type EleNode = {
    area?: number;
    baseKv?: number;
    bs?: number;
    busI?: number;
    gs?: number;
    id?: number;
    pd?: number;
    qd?: number;
    type?: number;
    va?: number;
    vm?: number;
    vmax?: number;
    vmin?: number;
    zone?: number;
  };

  type ElePipe = {
    angle?: number;
    b?: number;
    fbus?: number;
    id?: number;
    r?: number;
    rateA?: number;
    rateB?: number;
    rateC?: number;
    ratio?: number;
    status?: number;
    tbus?: number;
    x?: number;
  };

  type ExternalElectricity = {
    externalElectricity?: number;
    id?: number;
    time?: number;
  };

  type findByGensetNumberUsingGET1Params = {
    /** gensetNumber */
    gensetNumber: string;
  };

  type findByGensetNumberUsingGETParams = {
    /** gensetNumber */
    gensetNumber: string;
  };

  type GasElectricityPrice = {
    gasElectricityPrice?: number;
    hour?: number;
    id?: number;
  };

  type GasGen = {
    id?: number;
    installedCapacity?: number;
    mbase?: number;
    number?: number;
    pg?: number;
    pmax?: number;
    pmin?: number;
    qg?: number;
    qmax?: number;
    qmin?: number;
    status?: number;
    vg?: number;
  };

  type GasGensetCoefficient = {
    a?: number;
    b?: number;
    c?: number;
    id?: number;
  };

  type GasNode = {
    id?: number;
    maxPressure?: number;
    minPressure?: number;
    nodeLoad?: number;
    nodeNo?: number;
  };

  type GasPipe = {
    c?: number;
    diameter?: number;
    fnode?: number;
    id?: number;
    length?: number;
    m2?: number;
    pipe?: number;
    tnode?: number;
  };

  type GensetTypeAndCapacity = {
    gensetType?: string;
    id?: number;
    installedCapacity?: number;
  };

  type LoadAndNewEnergy = {
    id?: number;
    loadDemand?: number;
    newEnergyOutput?: number;
    time?: number;
  };

  type NewEnergyOutput = {
    genNumber?: number;
    hour1?: number;
    hour10?: number;
    hour11?: number;
    hour12?: number;
    hour13?: number;
    hour14?: number;
    hour15?: number;
    hour16?: number;
    hour17?: number;
    hour18?: number;
    hour19?: number;
    hour2?: number;
    hour20?: number;
    hour21?: number;
    hour22?: number;
    hour23?: number;
    hour24?: number;
    hour3?: number;
    hour4?: number;
    hour5?: number;
    hour6?: number;
    hour7?: number;
    hour8?: number;
    hour9?: number;
    id?: number;
  };

  type OrderItem = {
    asc?: boolean;
    column?: string;
  };

  type PageCoalGen = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: CoalGen[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageDistributedSmallCoalGasGen = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: DistributedSmallCoalGasGen[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageEleGenset = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: EleGenset[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageEleNode = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: EleNode[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageElePipe = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: ElePipe[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageExternalElectricity = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: ExternalElectricity[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageGasGen = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: GasGen[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageGasNode = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: GasNode[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageGasPipe = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: GasPipe[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageNewEnergyOutput = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: NewEnergyOutput[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type PageUnifiedSchedulingGen = {
    countId?: string;
    current?: number;
    maxLimit?: number;
    optimizeCountSql?: boolean;
    orders?: OrderItem[];
    pages?: number;
    records?: UnifiedSchedulingGen[];
    searchCount?: boolean;
    size?: number;
    total?: number;
  };

  type pageUsingGET10Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET1Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET2Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET3Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET4Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET5Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET6Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET7Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET8Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGET9Params = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type pageUsingGETParams = {
    /** current */
    current: number;
    /** size */
    size: number;
  };

  type PostDisasterFaultImpact = {
    id?: number;
    name?: string;
    value?: string;
  };

  type PreDisasterDispatchableResourceCapacity = {
    id?: number;
    name?: string;
    percentage?: string;
    value?: number;
  };

  type PreDisasterFlexibleResourceCapacity = {
    id?: number;
    name?: string;
    percentage?: string;
    value?: number;
  };

  type PreDisasterFlexResourceMetrics = {
    id?: number;
    name?: string;
    value?: string;
  };

  type PreDisasterScenario = {
    evaluationTime?: string;
    id?: number;
    name?: string;
    number?: string;
    toughnessIndex?: number;
  };

  type SystemLoad = {
    hour?: number;
    id?: number;
    systemLoad?: number;
  };

  type UnifiedSchedulingGen = {
    genNumber?: number;
    id?: number;
    mbase?: number;
    nodeNumber?: number;
    pg?: number;
    pmax?: number;
    pmin?: number;
    qg?: number;
    qmax?: number;
    qmin?: number;
    status?: number;
    vg?: number;
  };

  type updateUsingPUT10Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT11Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT12Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT13Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT14Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT15Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT16Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT17Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT1Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT2Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT3Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT4Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT5Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT6Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT7Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT8Params = {
    /** id */
    id: number;
  };

  type updateUsingPUT9Params = {
    /** id */
    id: number;
  };

  type updateUsingPUTParams = {
    /** id */
    id: number;
  };

  type WarningDisasterArea = {
    id?: number;
    name?: string;
    unit?: string;
    value?: string;
  };
}
